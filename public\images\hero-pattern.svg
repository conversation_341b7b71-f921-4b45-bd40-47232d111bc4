<?xml version="1.0" encoding="UTF-8"?>
<svg width="1440" height="800" viewBox="0 0 1440 800" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <pattern id="dots" x="0" y="0" width="40" height="40" patternUnits="userSpaceOnUse">
      <circle cx="20" cy="20" r="1.5" fill="#ffffff" opacity="0.5" />
    </pattern>
    
    <pattern id="grid" x="0" y="0" width="100" height="100" patternUnits="userSpaceOnUse">
      <path d="M 100 0 L 0 0 0 100" fill="none" stroke="#ffffff" stroke-width="0.5" opacity="0.2" />
    </pattern>
    
    <linearGradient id="wave-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#ffffff" stop-opacity="0.05" />
      <stop offset="100%" stop-color="#ffffff" stop-opacity="0" />
    </linearGradient>
  </defs>
  
  <!-- Background patterns -->
  <rect width="100%" height="100%" fill="url(#dots)" />
  <rect width="100%" height="100%" fill="url(#grid)" />
  
  <!-- Decorative waves -->
  <path d="M0,160 C320,300,420,0,740,120 C1060,240,1120,80,1440,160 L1440,800 L0,800 Z" fill="url(#wave-gradient)" />
  <path d="M0,320 C320,400,420,200,740,320 C1060,440,1120,280,1440,360 L1440,800 L0,800 Z" fill="url(#wave-gradient)" opacity="0.5" />
  
  <!-- Floating circles -->
  <circle cx="200" cy="200" r="80" fill="#ffffff" opacity="0.03" />
  <circle cx="1200" cy="300" r="120" fill="#ffffff" opacity="0.02" />
  <circle cx="600" cy="500" r="60" fill="#ffffff" opacity="0.04" />
  <circle cx="1000" cy="150" r="40" fill="#ffffff" opacity="0.03" />
</svg>
