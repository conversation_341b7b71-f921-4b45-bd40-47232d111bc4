{"name": "trex-media-website", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@sanity/client": "^7.5.0", "@sanity/image-url": "^1.1.0", "@types/react-router-dom": "^5.3.3", "embla-carousel-react": "^8.6.0", "framer-motion": "^12.6.3", "lucide-react": "^0.487.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-router-dom": "^7.5.0", "recharts": "^2.15.2"}, "devDependencies": {"@eslint/js": "^9.21.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react-swc": "^3.8.0", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "typescript": "~5.7.2", "typescript-eslint": "^8.24.1", "vite": "^6.2.0"}}