import { useState, useEffect } from 'react';
import { client } from '../../lib/sanity';

const SimpleBlogList = () => {
  const [posts, setPosts] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchPosts = async () => {
      try {
        const result = await client.fetch(`*[_type == "post"] | order(_createdAt desc) {
          _id,
          title,
          slug,
          publishedAt,
          "author": author->name,
          "categories": categories[]->title,
          body
        }`);
        
        setPosts(result);
      } catch (error) {
        console.error('Error fetching posts:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchPosts();
  }, []);

  if (loading) return <div>Loading simple blog list...</div>;

  return (
    <div style={{ padding: '20px', border: '2px solid orange', margin: '20px 0' }}>
      <h3>🧪 Simple Blog List (Bypass Processing)</h3>
      <div style={{ display: 'grid', gap: '20px' }}>
        {posts.map((post) => (
          <div key={post._id} style={{ 
            border: '1px solid #ccc', 
            padding: '15px', 
            borderRadius: '8px',
            backgroundColor: '#f9f9f9'
          }}>
            <h4>{post.title}</h4>
            <p><strong>Author:</strong> {post.author || 'Unknown'}</p>
            <p><strong>Categories:</strong> {post.categories?.join(', ') || 'None'}</p>
            <p><strong>Slug:</strong> {post.slug?.current || 'No slug'}</p>
            <p><strong>Published:</strong> {post.publishedAt ? new Date(post.publishedAt).toLocaleDateString() : 'Not set'}</p>
            <p><strong>Body blocks:</strong> {post.body?.length || 0}</p>
            {post.body && post.body.length > 0 && (
              <p><strong>First block text:</strong> {
                post.body[0]?.children?.[0]?.text?.substring(0, 100) + '...' || 'No text found'
              }</p>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default SimpleBlogList;
