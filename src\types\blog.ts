// TypeScript interfaces for blog data from Sanity

export interface SanityImage {
  _type: 'image';
  asset: {
    _ref: string;
    _type: 'reference';
  };
  hotspot?: {
    x: number;
    y: number;
    height: number;
    width: number;
  };
}

export interface Author {
  _id: string;
  name: string;
  image?: SanityImage;
  bio?: any[]; // Block content
}

export interface Category {
  _id: string;
  title: string;
  description?: string;
}

export interface BlogPost {
  _id: string;
  title: string;
  slug: {
    current: string;
  };
  publishedAt: string;
  mainImage?: SanityImage;
  author: string | Author; // Can be populated or just name
  categories: string[] | Category[]; // Can be populated or just titles
  body: any[]; // Block content array
  isWhatsAppContent: boolean;
  isPremium: boolean;
}

// Processed blog post for frontend use
export interface ProcessedBlogPost {
  id: string;
  title: string;
  slug: string;
  excerpt: string;
  category: string;
  author: string;
  date: string;
  image: string;
  tags: string[];
  readingTime: string;
  isWhatsAppContent: boolean;
  isPremium: boolean;
  body?: any[];
}

// API response types
export interface BlogPostsResponse {
  posts: ProcessedBlogPost[];
  total: number;
  hasMore: boolean;
}

export interface BlogFilters {
  category?: string;
  search?: string;
  limit?: number;
  offset?: number;
}
