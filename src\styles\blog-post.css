/* Blog Post Page Styles */

.blog-post-page {
  min-height: 100vh;
}

.blog-post-header {
  padding: 2rem 0;
  background: var(--color-background-secondary);
}

.blog-post-nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.back-link {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--color-primary);
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
}

.back-link:hover {
  color: var(--color-primary-dark);
}

.share-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: var(--color-primary);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.3s ease;
}

.share-button:hover {
  background: var(--color-primary-dark);
}

.blog-post-hero {
  text-align: center;
  margin-bottom: 2rem;
}

.blog-post-hero .post-category {
  display: inline-block;
  background: var(--color-primary);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 2rem;
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 1rem;
}

.blog-post-hero h1 {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--color-text-primary);
  margin-bottom: 1rem;
  line-height: 1.2;
}

.blog-post-hero .post-meta {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin-bottom: 1rem;
  flex-wrap: wrap;
}

.blog-post-hero .post-meta span {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--color-text-secondary);
  font-size: 0.875rem;
}

.blog-post-hero .post-tags {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.blog-post-hero .tag {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  background: var(--color-background);
  color: var(--color-text-secondary);
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.75rem;
  border: 1px solid var(--color-border);
}

.blog-post-image {
  margin-top: 2rem;
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.blog-post-image img {
  width: 100%;
  height: 400px;
  object-fit: cover;
}

.blog-post-content {
  padding: 4rem 0;
}

.blog-post-body {
  max-width: 800px;
  margin: 0 auto;
}

.content-excerpt {
  margin-bottom: 3rem;
  padding-bottom: 2rem;
  border-bottom: 1px solid var(--color-border);
}

.content-excerpt .excerpt {
  font-size: 1.25rem;
  line-height: 1.6;
  color: var(--color-text-secondary);
  font-style: italic;
  text-align: center;
}

.content-body {
  font-size: 1.125rem;
  line-height: 1.8;
  color: var(--color-text-primary);
}

.content-body p {
  margin-bottom: 1.5rem;
}

.content-body h2,
.content-body h3,
.content-body h4 {
  margin: 2rem 0 1rem 0;
  color: var(--color-text-primary);
}

.content-body h2 {
  font-size: 1.75rem;
}

.content-body h3 {
  font-size: 1.5rem;
}

.content-body h4 {
  font-size: 1.25rem;
}

.whatsapp-notice,
.premium-notice {
  background: var(--color-background-secondary);
  border: 1px solid var(--color-border);
  border-radius: 0.5rem;
  padding: 1.5rem;
  margin: 2rem 0;
}

.whatsapp-notice {
  border-left: 4px solid #25D366;
}

.premium-notice {
  border-left: 4px solid #FFD700;
}

.whatsapp-notice h4,
.premium-notice h4 {
  margin: 0 0 0.5rem 0;
  color: var(--color-text-primary);
}

.whatsapp-notice p,
.premium-notice p {
  margin: 0;
  color: var(--color-text-secondary);
}

.error-message {
  text-align: center;
  padding: 4rem 0;
}

.error-message h2 {
  color: var(--color-error, #dc3545);
  margin-bottom: 1rem;
}

.error-message p {
  margin-bottom: 2rem;
  color: var(--color-text-secondary);
}

/* Responsive Design */
@media (max-width: 768px) {
  .blog-post-nav {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .blog-post-hero h1 {
    font-size: 2rem;
  }

  .blog-post-hero .post-meta {
    flex-direction: column;
    gap: 0.5rem;
  }

  .blog-post-image img {
    height: 250px;
  }

  .content-excerpt .excerpt {
    font-size: 1.125rem;
  }

  .content-body {
    font-size: 1rem;
  }
}
