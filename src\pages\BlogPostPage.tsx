import { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Calendar, User, Clock, Tag, ArrowLeft, Share2 } from 'lucide-react';
import { getBlogPostBySlug } from '../services/blogService';
import { ProcessedBlogPost } from '../types/blog';
import '../styles/blog-post.css';

// Animation variants
const fadeIn = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0, transition: { duration: 0.6 } }
};

const BlogPostPage = () => {
  const { slug } = useParams<{ slug: string }>();
  const [post, setPost] = useState<ProcessedBlogPost | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchPost = async () => {
      if (!slug) {
        setError('No blog post specified');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);
        const postData = await getBlogPostBySlug(slug);
        
        if (!postData) {
          setError('Blog post not found');
        } else {
          setPost(postData);
        }
      } catch (err) {
        setError('Failed to load blog post');
        console.error('Error fetching blog post:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchPost();
  }, [slug]);

  const handleShare = async () => {
    if (navigator.share && post) {
      try {
        await navigator.share({
          title: post.title,
          text: post.excerpt,
          url: window.location.href,
        });
      } catch (err) {
        console.log('Error sharing:', err);
      }
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(window.location.href);
      alert('Link copied to clipboard!');
    }
  };

  if (loading) {
    return (
      <div className="blog-post-page">
        <div className="container">
          <div className="loading-spinner">
            <div className="spinner"></div>
            <p>Loading blog post...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error || !post) {
    return (
      <div className="blog-post-page">
        <div className="container">
          <div className="error-message">
            <h2>Blog Post Not Found</h2>
            <p>{error || 'The blog post you are looking for does not exist.'}</p>
            <Link to="/blog" className="button">
              <ArrowLeft size={16} /> Back to Blog
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="blog-post-page">
      {/* Header */}
      <section className="blog-post-header">
        <div className="container">
          <motion.div
            className="blog-post-nav"
            initial="hidden"
            animate="visible"
            variants={fadeIn}
          >
            <Link to="/blog" className="back-link">
              <ArrowLeft size={16} /> Back to Blog
            </Link>
            <button onClick={handleShare} className="share-button">
              <Share2 size={16} /> Share
            </button>
          </motion.div>

          <motion.div
            className="blog-post-hero"
            initial="hidden"
            animate="visible"
            variants={fadeIn}
          >
            <div className="post-category">{post.category}</div>
            <h1>{post.title}</h1>
            <div className="post-meta">
              <span><Calendar size={16} /> {post.date}</span>
              <span><User size={16} /> {post.author}</span>
              <span><Clock size={16} /> {post.readingTime}</span>
            </div>
            <div className="post-tags">
              {post.tags.map((tag, index) => (
                <span key={index} className="tag">
                  <Tag size={14} /> {tag}
                </span>
              ))}
            </div>
          </motion.div>

          <motion.div
            className="blog-post-image"
            initial="hidden"
            animate="visible"
            variants={fadeIn}
          >
            <img src={post.image} alt={post.title} />
          </motion.div>
        </div>
      </section>

      {/* Content */}
      <section className="blog-post-content">
        <div className="container">
          <motion.div
            className="blog-post-body"
            initial="hidden"
            animate="visible"
            variants={fadeIn}
          >
            <div className="content-excerpt">
              <p className="excerpt">{post.excerpt}</p>
            </div>
            
            {/* For now, we'll show a placeholder for the full content */}
            {/* In a real implementation, you'd render the Sanity block content here */}
            <div className="content-body">
              <p>This is where the full blog post content would be rendered from Sanity's block content.</p>
              <p>The blog post content is stored as structured data in Sanity and would need to be properly rendered using a block content renderer.</p>
              <p>For now, we're showing the excerpt: {post.excerpt}</p>
            </div>

            {post.isWhatsAppContent && (
              <div className="whatsapp-notice">
                <h4>📱 Available on WhatsApp TV</h4>
                <p>This content is also available on our WhatsApp TV channels for easy sharing and viewing.</p>
              </div>
            )}

            {post.isPremium && (
              <div className="premium-notice">
                <h4>⭐ Premium Content</h4>
                <p>This is premium content. Subscribe to access our full library of exclusive articles.</p>
              </div>
            )}
          </motion.div>
        </div>
      </section>

      {/* Newsletter Section */}
      <section className="newsletter-section section">
        <div className="container">
          <motion.div
            className="newsletter-container"
            initial="hidden"
            animate="visible"
            variants={fadeIn}
          >
            <div className="newsletter-content">
              <h2>Subscribe to Our Newsletter</h2>
              <p>Get the latest articles, news and updates delivered directly to your inbox.</p>
              <form className="newsletter-form">
                <input type="email" placeholder="Your email address" required />
                <button type="submit" className="button button-accent">Subscribe Now</button>
              </form>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default BlogPostPage;
