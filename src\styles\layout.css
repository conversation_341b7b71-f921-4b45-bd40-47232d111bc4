/* Header Styles */
.header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  background-color: var(--color-white);
  box-shadow: var(--shadow-sm);
  z-index: 1000;
  padding: var(--spacing-md) 0;
  transition: all var(--transition-normal);
}

.header-scrolled {
  padding: var(--spacing-sm) 0;
  background-color: var(--color-white);
  box-shadow: var(--shadow-md);
  backdrop-filter: blur(10px);
}

.header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
}

.logo h1 {
  font-size: 1.8rem;
  margin-bottom: 0;
  font-weight: 800;
  letter-spacing: -0.5px;
  transition: font-size var(--transition-normal);
}

.header-scrolled .logo h1 {
  font-size: 1.5rem;
}

.logo span {
  color: var(--color-primary);
  position: relative;
  display: inline-block;
}

.logo span::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background-color: var(--color-secondary);
  transform: scaleX(0);
  transform-origin: right;
  transition: transform var(--transition-normal);
}

.logo:hover span::after {
  transform: scaleX(1);
  transform-origin: left;
}

/* Desktop Navigation */
.desktop-nav {
  display: flex;
}

.desktop-nav ul {
  display: flex;
  list-style: none;
  gap: var(--spacing-lg);
  margin: 0;
  padding: 0;
}

.desktop-nav li {
  position: relative;
}

.desktop-nav a,
.dropdown-toggle {
  color: var(--color-black);
  font-weight: 600;
  font-size: 1rem;
  transition: all var(--transition-normal);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: 0.5rem 0.75rem;
  border-radius: var(--radius-md);
  position: relative;
  text-decoration: none;
}

.desktop-nav a::after,
.dropdown-toggle::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 2px;
  background-color: var(--color-primary);
  transition: all var(--transition-normal);
  transform: translateX(-50%);
  opacity: 0;
}

.desktop-nav a:hover,
.dropdown-toggle:hover {
  color: var(--color-primary);
  background-color: rgba(124, 58, 237, 0.05);
  transform: translateY(-2px);
}

.desktop-nav a:hover::after,
.dropdown-toggle:hover::after {
  width: 80%;
  opacity: 1;
}

.dropdown-toggle {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem 0.75rem;
  font-family: var(--font-body);
  font-weight: 600;
}

.dropdown-toggle svg {
  transition: transform var(--transition-normal);
}

.dropdown-toggle:hover svg {
  transform: rotate(180deg);
}

.dropdown {
  position: absolute;
  top: calc(100% + 0.5rem);
  left: 0;
  background-color: var(--color-white);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-lg);
  min-width: 220px;
  padding: var(--spacing-sm) 0;
  list-style: none;
  z-index: 100;
  border: 1px solid var(--color-gray-200);
  overflow: hidden;
}

.dropdown li {
  margin: 0;
}

.dropdown a {
  display: block;
  padding: var(--spacing-sm) var(--spacing-lg);
  color: var(--color-gray-700);
  font-weight: 500;
  transition: all var(--transition-normal);
  border-radius: 0;
  border-left: 3px solid transparent;
}

.dropdown a:hover {
  background-color: rgba(124, 58, 237, 0.05);
  color: var(--color-primary);
  border-left: 3px solid var(--color-primary);
  padding-left: calc(var(--spacing-lg) + 3px);
  transform: translateY(0);
}

.dropdown a::after {
  display: none;
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
  display: none;
  background: none;
  border: none;
  color: var(--color-black);
  cursor: pointer;
  padding: 0.5rem;
  border-radius: var(--radius-full);
  transition: all var(--transition-normal);
}

.mobile-menu-toggle:hover {
  background-color: rgba(124, 58, 237, 0.1);
  color: var(--color-primary);
  transform: rotate(5deg);
}

/* Active navigation item */
.desktop-nav a.active,
.mobile-nav a.active {
  color: var(--color-primary);
  font-weight: 700;
}

.desktop-nav a.active::after {
  width: 80%;
  opacity: 1;
}

/* Mobile Navigation */
.mobile-nav {
  display: none;
  position: fixed;
  top: 70px;
  right: 0;
  width: 80%;
  max-width: 350px;
  height: calc(100vh - 70px);
  background-color: var(--color-white);
  box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
  padding: var(--spacing-lg);
  overflow-y: auto;
  z-index: 999;
}

.mobile-nav ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.mobile-nav li {
  margin-bottom: var(--spacing-md);
}

.mobile-nav a,
.mobile-nav .dropdown-toggle {
  display: block;
  font-size: 1.1rem;
  padding: var(--spacing-sm) var(--spacing-md);
  margin: var(--spacing-xs) 0;
  border-radius: var(--radius-md);
  transition: all var(--transition-normal);
  border-left: 3px solid transparent;
}

.mobile-nav a:hover,
.mobile-nav .dropdown-toggle:hover {
  background-color: rgba(124, 58, 237, 0.05);
  border-left: 3px solid var(--color-primary);
  padding-left: calc(var(--spacing-md) + 5px);
}

.mobile-nav .dropdown {
  position: static;
  box-shadow: none;
  padding: 0 0 0 var(--spacing-lg);
  margin-top: var(--spacing-sm);
}

.mobile-nav .dropdown li {
  margin-bottom: var(--spacing-xs);
}

/* Footer Styles */
.footer {
  background-color: var(--color-gray-900);
  color: var(--color-white);
  padding: var(--spacing-2xl) 0 var(--spacing-lg);
  position: relative;
  overflow: hidden;
}

.footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at 10% 20%, rgba(124, 58, 237, 0.1) 0%, transparent 50%);
  z-index: 1;
}

.footer::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at 90% 80%, rgba(16, 185, 129, 0.1) 0%, transparent 50%);
  z-index: 1;
}

.footer .container {
  position: relative;
  z-index: 2;
}

.footer-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: var(--spacing-xl);
  margin-bottom: var(--spacing-2xl);
}

.footer-logo h2 {
  color: var(--color-white);
  margin-bottom: var(--spacing-sm);
}

.footer-logo span {
  color: var(--color-primary-light);
}

.footer-brand p {
  margin-bottom: var(--spacing-lg);
  opacity: 0.8;
}

.social-links {
  display: flex;
  gap: var(--spacing-md);
}

.social-links a {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: var(--radius-full);
  background-color: rgba(255, 255, 255, 0.1);
  color: var(--color-white);
  transition: all var(--transition-fast);
}

.social-links a:hover {
  background-color: var(--color-primary);
  transform: translateY(-3px);
}

.footer h3 {
  color: var(--color-white);
  font-size: 1.2rem;
  margin-bottom: var(--spacing-lg);
}

.footer-links ul,
.footer-contact ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-links li {
  margin-bottom: var(--spacing-sm);
}

.footer-links a {
  color: var(--color-gray-300);
  transition: color var(--transition-fast);
}

.footer-links a:hover {
  color: var(--color-primary-light);
}

.footer-contact li {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-md);
  color: var(--color-gray-300);
}

.footer-contact a {
  color: var(--color-gray-300);
}

.footer-contact .button {
  margin-top: var(--spacing-md);
}

.footer-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: var(--spacing-lg);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-bottom p {
  margin-bottom: 0;
  font-size: 0.9rem;
  opacity: 0.7;
}

.footer-legal {
  display: flex;
  gap: var(--spacing-lg);
}

.footer-legal a {
  font-size: 0.9rem;
  color: var(--color-gray-400);
}

/* Header Actions */
.header-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  margin-left: var(--spacing-md);
}

.dark-mode-toggle {
  background: transparent;
  color: var(--color-black);
  border: none;
  width: 40px;
  height: 40px;
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color var(--transition-fast), color var(--transition-fast), transform var(--transition-fast);
  box-shadow: none;
}

.dark-mode-toggle:hover {
  background-color: var(--color-gray-100);
  transform: rotate(15deg);
  box-shadow: none;
}

/* Scroll to Top Button */
.scroll-top-button {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 50px;
  height: 50px;
  border-radius: var(--radius-full);
  background-color: var(--color-primary);
  color: var(--color-white);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 99;
  box-shadow: var(--shadow-lg);
  border: none;
  transition: all var(--transition-fast);
}

.scroll-top-button:hover {
  background-color: var(--color-primary-light);
  transform: translateY(-5px);
}

/* Main Content Styles */
.main-content {
  margin-top: 70px; /* Header height */
  min-height: calc(100vh - 70px - 400px); /* Viewport height minus header and approximate footer height */
  transition: margin-top var(--transition-normal);
  width: 100vw;
  max-width: 100%;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.header-scrolled + .main-content {
  margin-top: 60px;
}

/* Responsive Styles */
@media (max-width: 1024px) {
  .footer-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .desktop-nav {
    display: none;
  }

  .mobile-menu-toggle {
    display: block;
  }

  .mobile-nav {
    display: block;
  }

  .footer-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-xl);
  }

  .footer-bottom {
    flex-direction: column;
    gap: var(--spacing-md);
    text-align: center;
  }
}

@media (max-width: 480px) {
  .header {
    padding: var(--spacing-sm) 0;
  }

  .logo h1 {
    font-size: 1.5rem;
  }

  .mobile-nav {
    width: 100%;
    max-width: none;
  }

  .footer {
    padding: var(--spacing-xl) 0 var(--spacing-md);
  }

  .footer-legal {
    flex-direction: column;
    gap: var(--spacing-sm);
  }
}