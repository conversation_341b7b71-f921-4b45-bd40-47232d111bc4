import { useState, useRef, useEffect } from 'react';
import { motion, useInView, AnimatePresence } from 'framer-motion';
import { Search, Calendar, User, Tag, ChevronRight, Clock, X, Filter } from 'lucide-react';
import { getAllBlogPosts, getAllCategories } from '../services/blogService';
import { ProcessedBlogPost } from '../types/blog';
import '../styles/blog.css';

// Animation variants
const fadeIn = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0, transition: { duration: 0.6 } }
};

const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2
    }
  }
};



const BlogPage = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [showFilters, setShowFilters] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [blogPosts, setBlogPosts] = useState<ProcessedBlogPost[]>([]);
  const [categories, setCategories] = useState<string[]>(['All']);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const postsPerPage = 6;

  // Refs for scroll animations
  const featuredRef = useRef<HTMLDivElement>(null);
  const blogListRef = useRef<HTMLDivElement>(null);

  // Check if sections are in view
  const featuredInView = useInView(featuredRef, { once: true, amount: 0.3 });
  const blogListInView = useInView(blogListRef, { once: true, amount: 0.3 });

  // Fetch blog posts and categories from Sanity
  useEffect(() => {
    const fetchData = async () => {
      try {
        console.log('🚀 BlogPage: Starting to fetch data...');
        setLoading(true);
        setError(null);

        const [postsResponse, categoriesData] = await Promise.all([
          getAllBlogPosts(),
          getAllCategories()
        ]);

        console.log('📊 BlogPage: Received data:', {
          posts: postsResponse.posts.length,
          categories: categoriesData.length
        });

        setBlogPosts(postsResponse.posts);
        setCategories(['All', ...categoriesData]);

        console.log('✅ BlogPage: State updated successfully');
      } catch (err) {
        console.error('❌ BlogPage: Error fetching blog data:', err);
        setError('Failed to load blog posts. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  // Reset to first page when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm, selectedCategory]);

  // Filter posts based on search and category
  const filteredPosts = blogPosts.filter(post => {
    const matchesSearch = post.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         post.excerpt.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         post.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
    const matchesCategory = selectedCategory === 'All' || post.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  // Pagination logic
  const indexOfLastPost = currentPage * postsPerPage;
  const indexOfFirstPost = indexOfLastPost - postsPerPage;
  const currentPosts = filteredPosts.slice(indexOfFirstPost, indexOfLastPost);
  const totalPages = Math.ceil(filteredPosts.length / postsPerPage);

  const paginate = (pageNumber: number) => {
    setCurrentPage(pageNumber);
    window.scrollTo({ top: blogListRef.current?.offsetTop || 0, behavior: 'smooth' });
  };

  const clearFilters = () => {
    setSearchTerm('');
    setSelectedCategory('All');
  };

  // Get featured post (first post)
  const featuredPost = blogPosts[0];

  // Show loading state
  if (loading) {
    return (
      <div className="blog-page">
        <section className="page-hero">
          <div className="container">
            <div className="page-hero-content">
              <h1>Trex Media Blog</h1>
              <p>Loading latest articles...</p>
            </div>
          </div>
        </section>
        <section className="section">
          <div className="container">
            <div className="loading-spinner">
              <div className="spinner"></div>
              <p>Loading blog posts...</p>
            </div>
          </div>
        </section>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="blog-page">
        <section className="page-hero">
          <div className="container">
            <div className="page-hero-content">
              <h1>Trex Media Blog</h1>
              <p>Something went wrong while loading the blog.</p>
            </div>
          </div>
        </section>
        <section className="section">
          <div className="container">
            <div className="error-message">
              <h3>Error Loading Blog Posts</h3>
              <p>{error}</p>
              <button
                className="button"
                onClick={() => window.location.reload()}
              >
                Try Again
              </button>
            </div>
          </div>
        </section>
      </div>
    );
  }

  return (
    <div className="blog-page">
      {/* Hero Section */}
      <section className="page-hero">
        <div className="container">
          <motion.div
            className="page-hero-content"
            initial="hidden"
            animate="visible"
            variants={staggerContainer}
          >
            <motion.h1 variants={fadeIn}>Trex Media Blog</motion.h1>
            <motion.p variants={fadeIn}>
              Insights, news, and updates from the Nigerian digital media landscape.
              Stay informed with our latest articles and industry trends.
            </motion.p>
          </motion.div>
        </div>
      </section>

      {/* Featured Post Section */}
      {featuredPost && (
        <section className="featured-post section" ref={featuredRef}>
          <div className="container">
            <motion.div
              className="section-header"
              initial="hidden"
              animate={featuredInView ? "visible" : "hidden"}
              variants={fadeIn}
            >
              <h2>Featured Article</h2>
              <div className="section-divider"></div>
            </motion.div>

            <motion.div
              className="featured-post-container"
              initial="hidden"
              animate={featuredInView ? "visible" : "hidden"}
              variants={fadeIn}
            >
              <div className="featured-post-image">
                <img src={featuredPost.image} alt={featuredPost.title} />
                <div className="post-category">{featuredPost.category}</div>
              </div>
              <div className="featured-post-content">
                <h3>{featuredPost.title}</h3>
                <div className="post-meta">
                  <span><Calendar size={14} /> {featuredPost.date}</span>
                  <span><User size={14} /> {featuredPost.author}</span>
                  <span><Clock size={14} /> {featuredPost.readingTime}</span>
                </div>
                <p>{featuredPost.excerpt}</p>
                <div className="post-tags">
                  {featuredPost.tags.map((tag, index) => (
                    <span key={index} className="tag"><Tag size={14} /> {tag}</span>
                  ))}
                </div>
                <a href={`/blog/${featuredPost.slug}`} className="button">Read More</a>
              </div>
            </motion.div>
          </div>
        </section>
      )}

      {/* Blog List Section */}
      <section className="blog-list section" ref={blogListRef}>
        <div className="container">
          <motion.div
            className="blog-filters"
            initial="hidden"
            animate={blogListInView ? "visible" : "hidden"}
            variants={fadeIn}
          >
            <div className="search-container">
              <Search size={18} />
              <input
                type="text"
                placeholder="Search articles..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
              {searchTerm && (
                <button
                  className="clear-search"
                  onClick={() => setSearchTerm('')}
                  aria-label="Clear search"
                >
                  <X size={16} />
                </button>
              )}
            </div>

            <div className="filter-controls">
              <button
                className="filter-toggle button-secondary button-sm"
                onClick={() => setShowFilters(!showFilters)}
              >
                <Filter size={16} /> {showFilters ? 'Hide Filters' : 'Show Filters'}
              </button>

              {(searchTerm || selectedCategory !== 'All') && (
                <button
                  className="clear-filters button-sm"
                  onClick={clearFilters}
                >
                  <X size={16} /> Clear Filters
                </button>
              )}
            </div>

            <AnimatePresence>
              {showFilters && (
                <motion.div
                  className="category-filters"
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  transition={{ duration: 0.3 }}
                >
                  {categories.map((category, index) => (
                    <button
                      key={index}
                      className={`category-filter ${selectedCategory === category ? 'active' : ''}`}
                      onClick={() => setSelectedCategory(category)}
                    >
                      {category}
                    </button>
                  ))}
                </motion.div>
              )}
            </AnimatePresence>
          </motion.div>

          {filteredPosts.length > 0 && (
            <motion.p
              className="results-count"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.3 }}
            >
              Showing {currentPosts.length} of {filteredPosts.length} articles
            </motion.p>
          )}

          <motion.div
            className="blog-grid"
            initial="hidden"
            animate={blogListInView ? "visible" : "hidden"}
            variants={staggerContainer}
          >
            {currentPosts.length > 0 ? (
              currentPosts.map((post) => (
                <motion.div key={post.id} className="blog-card" variants={fadeIn}>
                  <div className="blog-card-image">
                    <img src={post.image} alt={post.title} />
                    <div className="post-category">{post.category}</div>
                  </div>
                  <div className="blog-card-content">
                    <h3>{post.title}</h3>
                    <div className="post-meta">
                      <span><Calendar size={14} /> {post.date}</span>
                      <span><User size={14} /> {post.author}</span>
                    </div>
                    <p>{post.excerpt}</p>
                    <div className="card-footer">
                      <span className="reading-time"><Clock size={14} /> {post.readingTime}</span>
                      <a href={`/blog/${post.slug}`} className="text-link">
                        Read More <ChevronRight size={16} />
                      </a>
                    </div>
                  </div>
                </motion.div>
              ))
            ) : (
              <div className="no-results">
                <h3>No articles found</h3>
                <p>Try adjusting your search or category filters</p>
                <button className="button button-secondary" onClick={clearFilters}>
                  Clear Filters
                </button>
              </div>
            )}
          </motion.div>

          {/* Pagination */}
          {filteredPosts.length > postsPerPage && (
            <motion.div
              className="pagination"
              initial="hidden"
              animate={blogListInView ? "visible" : "hidden"}
              variants={fadeIn}
            >
              {Array.from({ length: totalPages }, (_, i) => i + 1).map(number => (
                <button
                  key={number}
                  className={`pagination-button ${currentPage === number ? 'active' : ''}`}
                  onClick={() => paginate(number)}
                >
                  {number}
                </button>
              ))}
              {currentPage < totalPages && (
                <button
                  className="pagination-button"
                  onClick={() => paginate(currentPage + 1)}
                >
                  Next
                </button>
              )}
            </motion.div>
          )}
        </div>
      </section>

      {/* Newsletter Section */}
      <section className="newsletter-section section">
        <div className="container">
          <motion.div
            className="newsletter-container"
            initial="hidden"
            animate="visible"
            variants={fadeIn}
          >
            <div className="newsletter-content">
              <h2>Subscribe to Our Newsletter</h2>
              <p>Get the latest articles, news and updates delivered directly to your inbox.</p>
              <form className="newsletter-form">
                <input type="email" placeholder="Your email address" required />
                <button type="submit" className="button button-accent">Subscribe Now</button>
              </form>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default BlogPage;