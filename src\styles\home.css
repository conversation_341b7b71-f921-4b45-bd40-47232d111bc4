/* Home Page Styles */

/* Loading placeholders */
.loading-placeholder {
  background: linear-gradient(90deg, var(--color-border) 25%, transparent 50%, var(--color-border) 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: 0.5rem;
  height: 200px;
  width: 100%;
}

.loading-placeholder-text {
  background: linear-gradient(90deg, var(--color-border) 25%, transparent 50%, var(--color-border) 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: 0.25rem;
  height: 1rem;
  margin-bottom: 0.5rem;
}

.loading-placeholder-text:last-child {
  width: 60%;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Hero Section */
.hero-section {
  padding: var(--spacing-3xl) 0;
  background: linear-gradient(135deg, #3a1772 0%, var(--color-primary) 80%, var(--color-secondary) 100%);
  color: var(--color-white);
  position: relative;
  overflow: hidden;
  min-height: 90vh;
  display: flex;
  align-items: center;
}

.hero-section::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.2) 0%, transparent 70%);
  z-index: 1;
}

.hero-section::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-image: url('/images/hero-pattern-new.svg');
  background-size: cover;
  opacity: 0.25;
  z-index: 1;
  animation: pulse 15s infinite alternate;
}

.hero-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-2xl);
  align-items: center;
  position: relative;
  z-index: 2;
}

.hero-content h1 {
  font-size: 3.5rem;
  margin-bottom: var(--spacing-lg);
  color: var(--color-white);
  line-height: 1.1;
  position: relative;
  display: inline-block;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  background: linear-gradient(to right, #ffffff, #e0e0ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 800;
}

.hero-content h1::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 0;
  width: 80px;
  height: 4px;
  background: linear-gradient(to right, var(--color-secondary), var(--color-primary));
  border-radius: var(--radius-full);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.hero-content p {
  font-size: 1.2rem;
  margin-bottom: var(--spacing-xl);
  opacity: 0.9;
  text-shadow: 0 1px 5px rgba(0, 0, 0, 0.2);
  font-weight: 500;
  line-height: 1.6;
  max-width: 600px;
}

.hero-buttons {
  display: flex;
  gap: var(--spacing-md);
}

.hero-buttons .button {
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.hero-buttons .button:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
  border-color: rgba(255, 255, 255, 0.4);
}

.hero-image img {
  width: 100%;
  height: auto;
  border-radius: var(--radius-lg);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-15px);
  }
  100% {
    transform: translateY(0px);
  }
}

/* Section Header */
.section-header {
  text-align: center;
  margin-bottom: var(--spacing-2xl);
}

.section-divider {
  width: 80px;
  height: 4px;
  background-color: var(--color-primary);
  margin: var(--spacing-md) auto var(--spacing-lg);
  border-radius: var(--radius-full);
}

/* About Section */
.about-section {
  background-color: var(--color-white);
}

.about-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-2xl);
  align-items: center;
}

.about-text p {
  margin-bottom: var(--spacing-md);
  font-size: 1.1rem;
  line-height: 1.7;
}

.founder-section {
  margin-top: var(--spacing-xl);
  margin-bottom: var(--spacing-xl);
  padding: var(--spacing-lg);
  background-color: rgba(124, 58, 237, 0.05);
  border-radius: var(--radius-lg);
  border-left: 4px solid var(--color-primary);
  display: flex;
  gap: var(--spacing-lg);
  align-items: center;
}

.founder-section h3 {
  font-size: 1.4rem;
  color: var(--color-primary);
  margin-bottom: var(--spacing-md);
}

.founder-section p {
  font-size: 1.05rem;
}

.founder-image {
  flex-shrink: 0;
  width: 180px;
  height: 180px;
  border-radius: 50%;
  overflow: hidden;
  border: 3px solid var(--color-primary);
  box-shadow: 0 5px 15px rgba(124, 58, 237, 0.3);
}

.founder-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.founder-content {
  flex: 1;
}

.founder-section h3 {
  font-size: 1.4rem;
  color: var(--color-primary);
  margin-bottom: var(--spacing-md);
}

.founder-section p {
  font-size: 1.05rem;
}

.about-image img {
  width: 100%;
  height: auto;
  border-radius: var(--radius-lg);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.text-link {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-weight: 600;
  margin-top: var(--spacing-md);
}

/* Services Section */
.services-section {
  background-color: var(--color-gray-100);
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacing-xl);
  margin-bottom: var(--spacing-2xl);
}

.service-card {
  background-color: var(--color-white);
  padding: var(--spacing-xl);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
  border: 1px solid var(--color-gray-200);
  z-index: 1;
}

.service-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, var(--color-primary-light) 0%, var(--color-primary) 100%);
  opacity: 0;
  z-index: -1;
  transition: opacity var(--transition-normal);
}

.service-card:hover {
  transform: translateY(-10px);
  box-shadow: var(--shadow-lg);
  border-color: transparent;
}

.service-card:hover::before {
  opacity: 0.05;
}

.service-card:hover h3,
.service-card:hover .service-icon {
  color: var(--color-primary);
}

.service-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 70px;
  height: 70px;
  background-color: rgba(124, 58, 237, 0.1);
  color: var(--color-primary);
  border-radius: var(--radius-full);
  margin-bottom: var(--spacing-md);
  transition: all var(--transition-normal);
  position: relative;
  z-index: 1;
}

.service-icon::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: var(--radius-full);
  border: 2px dashed var(--color-primary-light);
  opacity: 0;
  transform: scale(1.2);
  transition: all var(--transition-normal);
  z-index: -1;
}

.service-card:hover .service-icon::after {
  opacity: 1;
  transform: scale(1.1);
  animation: spin 10s linear infinite;
}

@keyframes spin {
  from { transform: scale(1.1) rotate(0deg); }
  to { transform: scale(1.1) rotate(360deg); }
}

.service-card h3 {
  margin-bottom: var(--spacing-sm);
}

.services-cta {
  text-align: center;
}

/* WhatsApp TV Section */
.whatsapp-tv-section {
  background-color: var(--color-white);
}

.whatsapp-channels {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacing-xl);
}

.channel-card {
  background-color: var(--color-white);
  border: 1px solid var(--color-gray-200);
  border-radius: var(--radius-lg);
  overflow: hidden;
  transition: transform var(--transition-normal), box-shadow var(--transition-normal);
}

.channel-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.channel-image {
  height: 200px;
  overflow: hidden;
}

.channel-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform var(--transition-normal);
}

.channel-card:hover .channel-image img {
  transform: scale(1.05);
}

.channel-card h3 {
  padding: var(--spacing-md) var(--spacing-md) var(--spacing-xs);
  margin-bottom: 0;
}

.channel-card p {
  padding: 0 var(--spacing-md);
  margin-bottom: var(--spacing-md);
  color: var(--color-gray-600);
}

.channel-card .button {
  margin: 0 var(--spacing-md) var(--spacing-md);
  display: block;
  text-align: center;
}

/* Blog Section */
.blog-section {
  background-color: var(--color-gray-50);
  position: relative;
  overflow: hidden;
}

.blog-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url('/images/dots-pattern.svg');
  opacity: 0.3;
  z-index: 1;
}

.blog-section .container {
  position: relative;
  z-index: 2;
}

.blog-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-xl);
  margin-bottom: var(--spacing-2xl);
}

.blog-card {
  background-color: var(--color-white);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
  border: 1px solid var(--color-gray-200);
  height: 100%;
  display: flex;
  flex-direction: column;
}

.blog-card:hover {
  transform: translateY(-10px);
  box-shadow: var(--shadow-xl);
  border-color: var(--color-primary-light);
}

.blog-image {
  height: 240px;
  overflow: hidden;
}

.blog-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform var(--transition-normal);
}

.blog-card:hover .blog-image img {
  transform: scale(1.05);
}

.blog-content {
  padding: var(--spacing-lg);
  flex: 1;
  display: flex;
  flex-direction: column;
}

.blog-date {
  display: block;
  font-size: 0.9rem;
  color: var(--color-gray-500);
  margin-bottom: var(--spacing-sm);
}

.blog-content h3 {
  margin-bottom: var(--spacing-sm);
  font-size: 1.4rem;
}

.blog-content p {
  margin-bottom: var(--spacing-md);
  color: var(--color-gray-600);
  flex: 1;
}

.blog-cta {
  text-align: center;
}

/* Testimonials Section */
.testimonials-section {
  background-color: var(--color-white);
  position: relative;
  overflow: hidden;
}

.testimonials-section::before {
  content: '';
  position: absolute;
  top: -30%;
  left: -30%;
  width: 60%;
  height: 60%;
  background: radial-gradient(circle, rgba(124, 58, 237, 0.05) 0%, transparent 70%);
  border-radius: 50%;
}

.testimonials-section::after {
  content: '';
  position: absolute;
  bottom: -30%;
  right: -30%;
  width: 60%;
  height: 60%;
  background: radial-gradient(circle, rgba(16, 185, 129, 0.05) 0%, transparent 70%);
  border-radius: 50%;
}

.testimonials-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacing-xl);
  margin-bottom: var(--spacing-2xl);
}

.testimonial-card {
  background-color: var(--color-white);
  border: 1px solid var(--color-gray-200);
  border-radius: var(--radius-lg);
  padding: var(--spacing-xl);
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
  position: relative;
}

.testimonial-card::before {
  content: '"';
  position: absolute;
  top: 20px;
  left: 20px;
  font-size: 5rem;
  line-height: 1;
  font-family: serif;
  color: var(--color-primary-light);
  opacity: 0.2;
}

.testimonial-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-lg);
  border-color: var(--color-primary-light);
}

.testimonial-content {
  margin-bottom: var(--spacing-lg);
}

.testimonial-content p {
  font-style: italic;
  color: var(--color-gray-700);
  line-height: 1.7;
  margin-bottom: 0;
}

.testimonial-author {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.testimonial-author img {
  width: 50px;
  height: 50px;
  border-radius: var(--radius-full);
  object-fit: cover;
}

.testimonial-author h4 {
  margin-bottom: 0;
  font-size: 1rem;
}

.testimonial-author p {
  margin-bottom: 0;
  font-size: 0.9rem;
  color: var(--color-gray-500);
}

.testimonials-stats {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: var(--spacing-xl);
  text-align: center;
}

.stat-item h3 {
  font-size: 2.5rem;
  color: var(--color-primary);
  margin-bottom: var(--spacing-xs);
}

.stat-item p {
  font-size: 1rem;
  color: var(--color-gray-600);
  margin-bottom: 0;
}

/* Promotions Section */
.promotions-section {
  background-color: var(--color-gray-100);
}

.promotions-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-2xl);
  align-items: center;
}

.promotions-text h3 {
  margin-bottom: var(--spacing-md);
}

.promotions-list {
  margin-bottom: var(--spacing-lg);
  padding-left: var(--spacing-lg);
}

.promotions-list li {
  margin-bottom: var(--spacing-sm);
  line-height: 1.6;
}

.promotions-image img {
  width: 100%;
  height: auto;
  border-radius: var(--radius-lg);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

/* Contact Section */
.contact-section {
  background-color: var(--color-white);
}

.contact-content {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: var(--spacing-2xl);
}

.contact-info h3 {
  margin-bottom: var(--spacing-md);
}

.contact-list {
  list-style: none;
  padding: 0;
  margin: var(--spacing-lg) 0 0;
}

.contact-list li {
  margin-bottom: var(--spacing-md);
}

.social-links-small {
  display: flex;
  gap: var(--spacing-md);
  margin-top: var(--spacing-xs);
}

.contact-form {
  background-color: var(--color-white);
  padding: var(--spacing-xl);
  border-radius: var(--radius-lg);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.form-group {
  margin-bottom: var(--spacing-md);
}

.form-group label {
  display: block;
  margin-bottom: var(--spacing-xs);
  font-weight: 600;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--color-gray-300);
  border-radius: var(--radius-md);
  font-family: var(--font-body);
  font-size: 1rem;
  transition: border-color var(--transition-fast);
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--color-primary);
}

/* Responsive Styles */
@media (max-width: 1024px) {
  .hero-content h1 {
    font-size: 3rem;
  }

  .services-grid,
  .whatsapp-channels,
  .testimonials-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .testimonials-stats {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-lg) var(--spacing-xl);
  }
}

@media (max-width: 768px) {
  .hero-container,
  .about-content,
  .promotions-content,
  .contact-content {
    grid-template-columns: 1fr;
  }

  .founder-section {
    flex-direction: column;
    text-align: center;
    padding: var(--spacing-md);
  }

  .founder-image {
    margin-bottom: var(--spacing-md);
  }

  .founder-content {
    grid-template-columns: 1fr;
  }

  .hero-content {
    text-align: center;
    order: 1;
  }

  .hero-image {
    order: 2;
  }

  .hero-buttons {
    justify-content: center;
  }

  .blog-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 640px) {
  .services-grid,
  .whatsapp-channels,
  .testimonials-grid {
    grid-template-columns: 1fr;
  }

  .hero-content h1 {
    font-size: 2.5rem;
  }

  .hero-buttons {
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  .testimonials-stats {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }
}