<?xml version="1.0" encoding="UTF-8"?>
<svg width="1440" height="800" viewBox="0 0 1440 800" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <pattern id="smallDots" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse">
      <circle cx="10" cy="10" r="1" fill="#ffffff" opacity="0.5" />
    </pattern>
    
    <pattern id="grid" x="0" y="0" width="80" height="80" patternUnits="userSpaceOnUse">
      <path d="M 80 0 L 0 0 0 80" fill="none" stroke="#ffffff" stroke-width="0.5" opacity="0.1" />
    </pattern>
    
    <linearGradient id="wave-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#ffffff" stop-opacity="0.1" />
      <stop offset="100%" stop-color="#ffffff" stop-opacity="0" />
    </linearGradient>
    
    <linearGradient id="overlay-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#ffffff" stop-opacity="0.05" />
      <stop offset="100%" stop-color="#ffffff" stop-opacity="0.15" />
    </linearGradient>
  </defs>
  
  <!-- Background patterns -->
  <rect width="100%" height="100%" fill="url(#smallDots)" />
  <rect width="100%" height="100%" fill="url(#grid)" />
  
  <!-- Decorative elements -->
  <circle cx="200" cy="200" r="100" fill="#ffffff" opacity="0.03" />
  <circle cx="1200" cy="300" r="150" fill="#ffffff" opacity="0.02" />
  <circle cx="600" cy="500" r="200" fill="#ffffff" opacity="0.01" />
  
  <!-- Abstract shapes -->
  <path d="M0,100 C320,200,420,50,740,150 C1060,250,1120,150,1440,200 L1440,800 L0,800 Z" fill="url(#wave-gradient)" />
  <path d="M0,250 C320,350,420,200,740,300 C1060,400,1120,300,1440,350 L1440,800 L0,800 Z" fill="url(#wave-gradient)" opacity="0.5" />
  
  <!-- Diagonal lines -->
  <g opacity="0.1" stroke="#ffffff" stroke-width="1">
    <line x1="0" y1="0" x2="1440" y2="800" />
    <line x1="200" y1="0" x2="1440" y2="700" />
    <line x1="400" y1="0" x2="1440" y2="600" />
    <line x1="600" y1="0" x2="1440" y2="500" />
    <line x1="800" y1="0" x2="1440" y2="400" />
    <line x1="1000" y1="0" x2="1440" y2="300" />
    <line x1="1200" y1="0" x2="1440" y2="200" />
  </g>
  
  <!-- Overlay gradient -->
  <rect width="100%" height="100%" fill="url(#overlay-gradient)" />
  
  <!-- Floating elements -->
  <g opacity="0.2">
    <rect x="100" y="100" width="40" height="40" rx="5" fill="#ffffff" transform="rotate(15)" />
    <rect x="1200" y="200" width="30" height="30" rx="5" fill="#ffffff" transform="rotate(45)" />
    <rect x="300" y="500" width="20" height="20" rx="5" fill="#ffffff" transform="rotate(30)" />
    <rect x="900" y="300" width="25" height="25" rx="5" fill="#ffffff" transform="rotate(60)" />
    <circle cx="500" cy="200" r="15" fill="#ffffff" />
    <circle cx="1100" cy="400" r="10" fill="#ffffff" />
    <circle cx="700" cy="600" r="12" fill="#ffffff" />
  </g>
</svg>
