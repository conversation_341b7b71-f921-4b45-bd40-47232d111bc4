import { Link } from 'react-router-dom';
import { Facebook, Twitter, Instagram, Youtube, Mail, Phone } from 'lucide-react';

const Footer = () => {
  const currentYear = new Date().getFullYear();
  
  return (
    <footer className="footer">
      <div className="container">
        <div className="footer-grid">
          <div className="footer-brand">
            <Link to="/" className="footer-logo">
              <h2>Trex<span>Media</span></h2>
            </Link>
            <p>A WhatsApp TV and entertainment platform based in Nigeria. Delivering engaging digital content across various niches.</p>
            <div className="social-links">
              <a href="https://facebook.com" target="_blank" rel="noopener noreferrer" aria-label="Facebook">
                <Facebook size={20} />
              </a>
              <a href="https://twitter.com" target="_blank" rel="noopener noreferrer" aria-label="Twitter">
                <Twitter size={20} />
              </a>
              <a href="https://instagram.com" target="_blank" rel="noopener noreferrer" aria-label="Instagram">
                <Instagram size={20} />
              </a>
              <a href="https://youtube.com" target="_blank" rel="noopener noreferrer" aria-label="Youtube">
                <Youtube size={20} />
              </a>
            </div>
          </div>
          
          <div className="footer-links">
            <h3>Quick Links</h3>
            <ul>
              <li><Link to="/">Home</Link></li>
              <li><Link to="/services">Services</Link></li>
              <li><Link to="/blog">Blog</Link></li>
              <li><Link to="/join-tv">Join TV</Link></li>
              <li><Link to="/testimonials">Testimonials</Link></li>
            </ul>
          </div>
          
          <div className="footer-links">
            <h3>Services</h3>
            <ul>
              <li><Link to="/services#content-creation">Content Creation</Link></li>
              <li><Link to="/services#whatsapp-tv">WhatsApp TV</Link></li>
              <li><Link to="/services#digital-marketing">Digital Marketing</Link></li>
              <li><Link to="/promote">Advertise With Us</Link></li>
            </ul>
          </div>
          
          <div className="footer-contact">
            <h3>Contact Us</h3>
            <ul>
              <li>
                <Mail size={16} />
                <a href="mailto:<EMAIL>"><EMAIL></a>
              </li>
              <li>
                <Phone size={16} />
                <a href="tel:+2341234567890">+234 ************</a>
              </li>
            </ul>
            <Link to="/contact" className="button button-secondary">Send Message</Link>
          </div>
        </div>
        
        <div className="footer-bottom">
          <p>&copy; {currentYear} Trex Media. All rights reserved.</p>
          <div className="footer-legal">
            <Link to="/privacy-policy">Privacy Policy</Link>
            <Link to="/terms-of-service">Terms of Service</Link>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;