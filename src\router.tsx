import { createBrowserRouter } from 'react-router-dom';
import App from './App';

// Import page components (to be created)
import HomePage from './pages/HomePage';
import ServicesPage from './pages/ServicesPage';
import BlogPage from './pages/BlogPage';
import BlogPostPage from './pages/BlogPostPage';
import JoinTvPage from './pages/JoinTvPage';
import TestimonialsPage from './pages/TestimonialsPage';
import PromotePage from './pages/PromotePage';
import ContactPage from './pages/ContactPage';

// Create and export the router configuration
const router = createBrowserRouter([
  {
    path: '/',
    element: <App />,
    children: [
      {
        index: true,
        element: <HomePage />,
      },
      {
        path: 'services',
        element: <ServicesPage />,
      },
      {
        path: 'blog',
        element: <BlogPage />,
      },
      {
        path: 'blog/:slug',
        element: <BlogPostPage />,
      },
      {
        path: 'join-tv',
        element: <JoinTvPage />,
      },
      {
        path: 'testimonials',
        element: <TestimonialsPage />,
      },
      {
        path: 'promote',
        element: <PromotePage />,
      },
      {
        path: 'contact',
        element: <ContactPage />,
      },
    ],
  },
]);

export default router;