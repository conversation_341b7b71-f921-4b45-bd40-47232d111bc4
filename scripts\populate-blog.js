const { createClient } = require('@sanity/client');

// Create Sanity client
const client = createClient({
  projectId: '535k6265',
  dataset: 'production',
  useCdn: false,
  token: process.env.SANITY_AUTH_TOKEN, // You'll need to set this
  apiVersion: '2024-01-01',
});

// Sample blog posts data
const samplePosts = [
  {
    title: 'The Rise of WhatsApp TV in Nigeria',
    slug: 'rise-of-whatsapp-tv-nigeria',
    excerpt: 'How WhatsApp TV channels are revolutionizing content consumption in Nigeria and creating new opportunities for creators and businesses.',
    category: 'Industry Insights',
    author: '<PERSON>',
    publishedAt: '2024-01-15T10:00:00Z',
    body: [
      {
        _type: 'block',
        children: [
          {
            _type: 'span',
            text: 'WhatsApp TV has emerged as a revolutionary platform in Nigeria, transforming how people consume digital content. This phenomenon represents a unique adaptation of social media for content distribution, particularly suited to the Nigerian market.'
          }
        ]
      },
      {
        _type: 'block',
        children: [
          {
            _type: 'span',
            text: 'The accessibility and low data requirements of WhatsApp make it an ideal platform for reaching audiences across different economic backgrounds. Content creators are leveraging this to build engaged communities and monetize their content effectively.'
          }
        ]
      },
      {
        _type: 'block',
        children: [
          {
            _type: 'span',
            text: 'At Trex Media, we\'ve witnessed firsthand how WhatsApp TV channels can create meaningful connections between content creators and their audiences, fostering a new era of digital entertainment in Nigeria.'
          }
        ]
      }
    ],
    isWhatsAppContent: true,
    isPremium: false
  },
  {
    title: 'Content Creation Strategies for Nigerian Audiences',
    slug: 'content-creation-strategies-nigerian-audiences',
    excerpt: 'Effective strategies for creating engaging content that resonates with Nigerian audiences across different demographics and interests.',
    category: 'Content Creation',
    author: 'Jackson Abetianbe',
    publishedAt: '2024-01-10T14:30:00Z',
    body: [
      {
        _type: 'block',
        children: [
          {
            _type: 'span',
            text: 'Creating content for Nigerian audiences requires understanding the diverse cultural landscape, languages, and preferences that make up our vibrant nation. Success in this space demands authenticity and cultural sensitivity.'
          }
        ]
      },
      {
        _type: 'block',
        children: [
          {
            _type: 'span',
            text: 'Key strategies include incorporating local languages, referencing popular culture, addressing relevant social issues, and maintaining a tone that resonates with your target demographic. Visual content should reflect the diversity and richness of Nigerian culture.'
          }
        ]
      },
      {
        _type: 'block',
        children: [
          {
            _type: 'span',
            text: 'Timing is also crucial - understanding when your audience is most active and tailoring your content schedule accordingly can significantly impact engagement rates.'
          }
        ]
      }
    ],
    isWhatsAppContent: false,
    isPremium: false
  },
  {
    title: 'Digital Marketing Trends in West Africa for 2024',
    slug: 'digital-marketing-trends-west-africa-2024',
    excerpt: 'Exploring the latest digital marketing trends shaping the West African market and how businesses can leverage them for growth.',
    category: 'Digital Marketing',
    author: 'Jackson Abetianbe',
    publishedAt: '2024-01-05T09:15:00Z',
    body: [
      {
        _type: 'block',
        children: [
          {
            _type: 'span',
            text: 'The digital marketing landscape in West Africa is evolving rapidly, with mobile-first strategies taking center stage. Social commerce, influencer partnerships, and community-driven marketing are becoming increasingly important.'
          }
        ]
      },
      {
        _type: 'block',
        children: [
          {
            _type: 'span',
            text: 'WhatsApp Business, Instagram Shopping, and TikTok marketing are gaining significant traction. Brands that adapt to these platforms and understand local consumer behavior are seeing remarkable growth.'
          }
        ]
      },
      {
        _type: 'block',
        children: [
          {
            _type: 'span',
            text: 'The key to success lies in creating authentic, culturally relevant content that speaks to local values while leveraging the latest digital tools and platforms.'
          }
        ]
      }
    ],
    isWhatsAppContent: false,
    isPremium: true
  },
  {
    title: 'Building Community Through WhatsApp Groups',
    slug: 'building-community-whatsapp-groups',
    excerpt: 'How to create and nurture engaged communities through WhatsApp groups and channels for better audience retention.',
    category: 'Community',
    author: 'Jackson Abetianbe',
    publishedAt: '2023-12-28T16:45:00Z',
    body: [
      {
        _type: 'block',
        children: [
          {
            _type: 'span',
            text: 'WhatsApp groups and channels offer unique opportunities for building tight-knit communities. Unlike other social platforms, WhatsApp provides a more intimate setting for meaningful interactions.'
          }
        ]
      },
      {
        _type: 'block',
        children: [
          {
            _type: 'span',
            text: 'Successful community building on WhatsApp requires consistent value delivery, active moderation, and creating spaces where members feel heard and valued. Regular engagement and exclusive content help maintain interest.'
          }
        ]
      },
      {
        _type: 'block',
        children: [
          {
            _type: 'span',
            text: 'The key is to balance promotional content with genuine community value, ensuring that members see the group as a resource rather than just a marketing channel.'
          }
        ]
      }
    ],
    isWhatsAppContent: true,
    isPremium: false
  }
];

// Function to create or get author
async function createAuthor(name) {
  try {
    // Check if author already exists
    const existingAuthor = await client.fetch(`*[_type == "author" && name == $name][0]`, { name });
    
    if (existingAuthor) {
      return existingAuthor._id;
    }

    // Create new author
    const author = await client.create({
      _type: 'author',
      name: name,
      slug: {
        current: name.toLowerCase().replace(/\s+/g, '-')
      },
      bio: [
        {
          _type: 'block',
          children: [
            {
              _type: 'span',
              text: `${name} is a content creator and digital marketing expert at Trex Media, passionate about creating engaging content for Nigerian audiences.`
            }
          ]
        }
      ]
    });

    return author._id;
  } catch (error) {
    console.error('Error creating author:', error);
    throw error;
  }
}

// Function to create or get category
async function createCategory(title) {
  try {
    // Check if category already exists
    const existingCategory = await client.fetch(`*[_type == "category" && title == $title][0]`, { title });
    
    if (existingCategory) {
      return existingCategory._id;
    }

    // Create new category
    const category = await client.create({
      _type: 'category',
      title: title,
      description: `Articles related to ${title.toLowerCase()}`
    });

    return category._id;
  } catch (error) {
    console.error('Error creating category:', error);
    throw error;
  }
}

// Main function to populate blog posts
async function populateBlogPosts() {
  try {
    console.log('Starting to populate blog posts...');

    for (const postData of samplePosts) {
      console.log(`Creating post: ${postData.title}`);

      // Check if post already exists
      const existingPost = await client.fetch(`*[_type == "post" && slug.current == $slug][0]`, { slug: postData.slug });
      
      if (existingPost) {
        console.log(`Post "${postData.title}" already exists, skipping...`);
        continue;
      }

      // Create or get author and category
      const authorId = await createAuthor(postData.author);
      const categoryId = await createCategory(postData.category);

      // Create the blog post
      const post = await client.create({
        _type: 'post',
        title: postData.title,
        slug: {
          current: postData.slug
        },
        author: {
          _type: 'reference',
          _ref: authorId
        },
        categories: [
          {
            _type: 'reference',
            _ref: categoryId
          }
        ],
        publishedAt: postData.publishedAt,
        body: postData.body,
        isWhatsAppContent: postData.isWhatsAppContent,
        isPremium: postData.isPremium
      });

      console.log(`✅ Created post: ${post.title}`);
    }

    console.log('🎉 All blog posts created successfully!');
  } catch (error) {
    console.error('❌ Error populating blog posts:', error);
  }
}

// Run the script
populateBlogPosts();
