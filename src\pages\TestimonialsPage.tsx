import { useRef } from 'react';
import { motion, useInView } from 'framer-motion';
import { Star, User, Building, Quote } from 'lucide-react';

// Animation variants
const fadeIn = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0, transition: { duration: 0.6 } }
};

const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2
    }
  }
};

// Mock testimonial data
const testimonials = [
  {
    id: 1,
    name: '<PERSON><PERSON><PERSON><PERSON><PERSON> Adeyemi',
    position: 'Marketing Director',
    company: 'Lagos Digital Solutions',
    image: '/images/testimonials/testimonial-1.png',
    rating: 5,
    text: 'Trex Media has been instrumental in our digital marketing success. Their WhatsApp TV channels helped us reach a wider audience and increased our brand visibility significantly.'
  },
  {
    id: 2,
    name: '<PERSON><PERSON><PERSON>',
    position: 'Content Creator',
    company: 'Naija Vibes',
    image: '/images/testimonials/testimonial-2.png',
    rating: 5,
    text: 'The engagement on my content has increased tenfold since partnering with Trex Media. Their understanding of the Nigerian audience is unmatched.'
  },
  {
    id: 3,
    name: '<PERSON>',
    position: 'CEO',
    company: 'TechNaija Startups',
    image: '/images/testimonials/testimonial-3.png',
    rating: 4,
    text: 'As a startup founder, finding the right platform to promote our services was challenging until we discovered Trex Media. Their targeted approach helped us connect with the right audience.'
  },
  {
    id: 4,
    name: 'Folake Adeola',
    position: 'Brand Manager',
    company: 'Sunshine Foods',
    image: '/images/testimonials/testimonial-4.png',
    rating: 5,
    text: 'The team at Trex Media understands the Nigerian market deeply. Their content strategy helped us launch our new product line with tremendous success.'
  },
  {
    id: 5,
    name: 'Emmanuel Osei',
    position: 'Digital Strategist',
    company: 'West African Media Group',
    image: '/images/testimonials/testimonial-5.png',
    rating: 5,
    text: 'Working with Trex Media has been a game-changer for our clients. Their WhatsApp TV channels provide incredible reach and engagement metrics that exceed industry standards.'
  },
  {
    id: 6,
    name: 'Amina Bello',
    position: 'Small Business Owner',
    company: 'Amina\'s Fashion',
    image: '/images/testimonials/testimonial-6.png',
    rating: 4,
    text: 'As a small business owner, I was skeptical about digital marketing, but Trex Media made it accessible and effective. My customer base has grown significantly since our partnership began.'
  }
];

// Case studies data
const caseStudies = [
  {
    id: 1,
    title: 'Increasing Brand Awareness for Lagos Digital Solutions',
    challenge: 'Lagos Digital Solutions needed to increase brand visibility in a competitive market.',
    solution: 'We created a targeted WhatsApp TV campaign focusing on their unique services and value proposition.',
    results: ['45% increase in brand awareness', '32% growth in lead generation', '28% increase in conversion rate'],
    image: '/images/case-studies/case-study-1.png'
  },
  {
    id: 2,
    title: 'Product Launch Campaign for Sunshine Foods',
    challenge: 'Sunshine Foods needed to create buzz around their new product line launch.',
    solution: 'We developed an integrated content strategy across our WhatsApp TV channels and social media platforms.',
    results: ['50,000+ people reached in the first week', '300% increase in product inquiries', '85% positive sentiment in audience feedback'],
    image: '/images/case-studies/case-study-2.png'
  },
  {
    id: 3,
    title: 'Growing Audience for Naija Vibes Content Creator',
    challenge: 'Chidinma from Naija Vibes wanted to expand her audience and increase engagement.',
    solution: 'We featured her content on our entertainment channels and created collaborative content opportunities.',
    results: ['10x increase in content engagement', '15,000 new followers across platforms', 'Multiple brand partnership opportunities generated'],
    image: '/images/case-studies/case-study-3.png'
  }
];

const TestimonialsPage = () => {
  // Refs for scroll animations
  const clientsRef = useRef<HTMLDivElement>(null);
  const caseStudiesRef = useRef<HTMLDivElement>(null);
  const mediaKitRef = useRef<HTMLDivElement>(null);
  
  // Check if sections are in view
  const clientsInView = useInView(clientsRef, { once: true, amount: 0.3 });
  const caseStudiesInView = useInView(caseStudiesRef, { once: true, amount: 0.3 });
  const mediaKitInView = useInView(mediaKitRef, { once: true, amount: 0.3 });

  // Function to render star ratings
  const renderStars = (rating: number) => {
    return Array(5).fill(0).map((_, index) => (
      <Star 
        key={index} 
        size={16} 
        fill={index < rating ? 'var(--color-primary)' : 'none'} 
        color={index < rating ? 'var(--color-primary)' : 'var(--color-gray-400)'} 
      />
    ));
  };

  return (
    <div className="testimonials-page">
      {/* Hero Section */}
      <section className="page-hero">
        <div className="container">
          <motion.div 
            className="page-hero-content"
            initial="hidden"
            animate="visible"
            variants={staggerContainer}
          >
            <motion.h1 variants={fadeIn}>Client Success Stories</motion.h1>
            <motion.p variants={fadeIn}>
              Discover how Trex Media has helped businesses and individuals achieve their 
              digital media goals with our innovative WhatsApp TV channels and content strategies.
            </motion.p>
          </motion.div>
        </div>
      </section>

      {/* Client Testimonials Section */}
      <section className="testimonials-section section" ref={clientsRef}>
        <div className="container">
          <motion.div 
            className="section-header"
            initial="hidden"
            animate={clientsInView ? "visible" : "hidden"}
            variants={fadeIn}
          >
            <h2>What Our Clients Say</h2>
            <div className="section-divider"></div>
            <p>Hear from businesses and individuals who have partnered with us</p>
          </motion.div>
          
          <motion.div 
            className="testimonials-grid"
            initial="hidden"
            animate={clientsInView ? "visible" : "hidden"}
            variants={staggerContainer}
          >
            {testimonials.map((testimonial) => (
              <motion.div className="testimonial-card" key={testimonial.id} variants={fadeIn}>
                <div className="testimonial-header">
                  <div className="testimonial-image">
                    <img src={testimonial.image} alt={testimonial.name} />
                  </div>
                  <div className="testimonial-meta">
                    <h3>{testimonial.name}</h3>
                    <p className="testimonial-position">
                      <User size={14} /> {testimonial.position}
                    </p>
                    <p className="testimonial-company">
                      <Building size={14} /> {testimonial.company}
                    </p>
                    <div className="testimonial-rating">
                      {renderStars(testimonial.rating)}
                    </div>
                  </div>
                </div>
                <div className="testimonial-content">
                  <Quote size={24} className="quote-icon" />
                  <p>{testimonial.text}</p>
                </div>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Case Studies Section */}
      <section className="case-studies-section section alternate-bg" ref={caseStudiesRef}>
        <div className="container">
          <motion.div 
            className="section-header"
            initial="hidden"
            animate={caseStudiesInView ? "visible" : "hidden"}
            variants={fadeIn}
          >
            <h2>Case Studies</h2>
            <div className="section-divider"></div>
            <p>Detailed examples of how we've helped our clients achieve their goals</p>
          </motion.div>
          
          <motion.div 
            className="case-studies-container"
            initial="hidden"
            animate={caseStudiesInView ? "visible" : "hidden"}
            variants={staggerContainer}
          >
            {caseStudies.map((study) => (
              <motion.div className="case-study-card" key={study.id} variants={fadeIn}>
                <div className="case-study-image">
                  <img src={study.image} alt={study.title} />
                </div>
                <div className="case-study-content">
                  <h3>{study.title}</h3>
                  <div className="case-study-details">
                    <div className="case-study-section">
                      <h4>Challenge</h4>
                      <p>{study.challenge}</p>
                    </div>
                    <div className="case-study-section">
                      <h4>Solution</h4>
                      <p>{study.solution}</p>
                    </div>
                    <div className="case-study-section">
                      <h4>Results</h4>
                      <ul>
                        {study.results.map((result, index) => (
                          <li key={index}>{result}</li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Media Kit Section */}
      <section className="media-kit-section section" ref={mediaKitRef}>
        <div className="container">
          <motion.div 
            className="media-kit-container"
            initial="hidden"
            animate={mediaKitInView ? "visible" : "hidden"}
            variants={fadeIn}
          >
            <div className="media-kit-content">
              <h2>Download Our Media Kit</h2>
              <p>
                Interested in learning more about our audience demographics, reach, and 
                partnership opportunities? Download our comprehensive media kit for 
                detailed information about our platforms and services.
              </p>
              <ul className="media-kit-features">
                <li>Audience demographics and insights</li>
                <li>Channel performance metrics</li>
                <li>Partnership and advertising options</li>
                <li>Case studies and success stories</li>
                <li>Pricing and package information</li>
              </ul>
              <a href="/downloads/trex-media-kit.pdf" className="button" download>
                Download Media Kit
              </a>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default TestimonialsPage;