import { useRef, useState } from 'react';
import { motion, useInView, AnimatePresence } from 'framer-motion';
import { MessageSquare, Users, Heart, Briefcase, Film, Music, Newspaper, ChevronDown, Star, Check, AlertCircle } from 'lucide-react';

// Animation variants
const fadeIn = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0, transition: { duration: 0.6 } }
};

const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2
    }
  }
};

const JoinTvPage = () => {
  // State for FAQ accordion
  const [activeFaq, setActiveFaq] = useState<number | null>(null);

  // Refs for scroll animations
  const entertainmentRef = useRef<HTMLDivElement>(null);
  const businessRef = useRef<HTMLDivElement>(null);
  const romanceRef = useRef<HTMLDivElement>(null);
  const faqRef = useRef<HTMLDivElement>(null);
  const howItWorksRef = useRef<HTMLDivElement>(null);
  const premiumRef = useRef<HTMLDivElement>(null);

  // Check if sections are in view
  const entertainmentInView = useInView(entertainmentRef, { once: true, amount: 0.3 });
  const businessInView = useInView(businessRef, { once: true, amount: 0.3 });
  const romanceInView = useInView(romanceRef, { once: true, amount: 0.3 });
  const faqInView = useInView(faqRef, { once: true, amount: 0.3 });
  const howItWorksInView = useInView(howItWorksRef, { once: true, amount: 0.3 });
  const premiumInView = useInView(premiumRef, { once: true, amount: 0.3 });

  // Toggle FAQ accordion
  const toggleFaq = (index: number) => {
    setActiveFaq(activeFaq === index ? null : index);
  };

  return (
    <div className="join-tv-page">
      {/* Hero Section */}
      <section className="page-hero">
        <div className="container">
          <motion.div
            className="page-hero-content"
            initial="hidden"
            animate="visible"
            variants={staggerContainer}
          >
            <motion.h1 variants={fadeIn}>Join Our WhatsApp TV Channels</motion.h1>
            <motion.p variants={fadeIn}>
              Connect with our diverse WhatsApp TV channels and receive daily updates,
              entertainment, and valuable content directly on your phone.
            </motion.p>
          </motion.div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="how-it-works section" ref={howItWorksRef}>
        <div className="container">
          <motion.div
            className="section-header"
            initial="hidden"
            animate={howItWorksInView ? "visible" : "hidden"}
            variants={fadeIn}
          >
            <h2>How It Works</h2>
            <div className="section-divider"></div>
            <p>Join our WhatsApp TV channels in three simple steps</p>
          </motion.div>

          <motion.div
            className="steps-container"
            initial="hidden"
            animate={howItWorksInView ? "visible" : "hidden"}
            variants={staggerContainer}
          >
            <motion.div className="step-card" variants={fadeIn}>
              <div className="step-number">1</div>
              <h3>Choose Your Channel</h3>
              <p>Select from our various niche channels based on your interests</p>
            </motion.div>

            <motion.div className="step-card" variants={fadeIn}>
              <div className="step-number">2</div>
              <h3>Click Join Button</h3>
              <p>Click the join button for your selected channel</p>
            </motion.div>

            <motion.div className="step-card" variants={fadeIn}>
              <div className="step-number">3</div>
              <h3>Start Receiving Updates</h3>
              <p>You'll be added to the channel and start receiving daily content</p>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Entertainment Channels Section */}
      <section id="entertainment" className="tv-channels-section section" ref={entertainmentRef}>
        <div className="container">
          <motion.div
            className="section-header"
            initial="hidden"
            animate={entertainmentInView ? "visible" : "hidden"}
            variants={fadeIn}
          >
            <h2>Entertainment Channels</h2>
            <div className="section-divider"></div>
            <p>Stay updated with the latest in entertainment, movies, music, and celebrity news</p>
          </motion.div>

          <motion.div
            className="channels-grid"
            initial="hidden"
            animate={entertainmentInView ? "visible" : "hidden"}
            variants={staggerContainer}
          >
            <motion.div className="channel-card" variants={fadeIn}>
              <div className="channel-icon">
                <Film size={32} />
              </div>
              <h3>Movies & TV</h3>
              <p>Latest movies, TV shows, reviews, and entertainment news from Nigeria and around the world.</p>
              <ul className="channel-features">
                <li>Daily movie recommendations</li>
                <li>New release updates</li>
                <li>Exclusive behind-the-scenes content</li>
                <li>Movie quotes and trivia</li>
              </ul>
              <a href="https://wa.me/1234567890?text=Join%20Movies%20TV%20Channel" className="button" target="_blank" rel="noopener noreferrer">
                Join Now <MessageSquare size={16} />
              </a>
            </motion.div>

            <motion.div className="channel-card" variants={fadeIn}>
              <div className="channel-icon">
                <Music size={32} />
              </div>
              <h3>Music Vibes</h3>
              <p>Stay updated with the latest music releases, artist news, and trending songs in Nigeria and globally.</p>
              <ul className="channel-features">
                <li>New music alerts</li>
                <li>Artist spotlights</li>
                <li>Concert and event updates</li>
                <li>Music charts and rankings</li>
              </ul>
              <a href="https://wa.me/1234567890?text=Join%20Music%20Vibes%20Channel" className="button" target="_blank" rel="noopener noreferrer">
                Join Now <MessageSquare size={16} />
              </a>
            </motion.div>

            <motion.div className="channel-card" variants={fadeIn}>
              <div className="channel-icon">
                <Users size={32} />
              </div>
              <h3>Celebrity Gist</h3>
              <p>All the latest celebrity news, gossip, and lifestyle updates from your favorite Nigerian and international stars.</p>
              <ul className="channel-features">
                <li>Celebrity news and updates</li>
                <li>Exclusive interviews</li>
                <li>Red carpet events</li>
                <li>Celebrity lifestyle content</li>
              </ul>
              <a href="https://wa.me/1234567890?text=Join%20Celebrity%20Gist%20Channel" className="button" target="_blank" rel="noopener noreferrer">
                Join Now <MessageSquare size={16} />
              </a>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Business Channels Section */}
      <section id="business" className="tv-channels-section section alternate-bg" ref={businessRef}>
        <div className="container">
          <motion.div
            className="section-header"
            initial="hidden"
            animate={businessInView ? "visible" : "hidden"}
            variants={fadeIn}
          >
            <h2>Business Channels</h2>
            <div className="section-divider"></div>
            <p>Get valuable business insights, market trends, and entrepreneurship tips</p>
          </motion.div>

          <motion.div
            className="channels-grid"
            initial="hidden"
            animate={businessInView ? "visible" : "hidden"}
            variants={staggerContainer}
          >
            <motion.div className="channel-card" variants={fadeIn}>
              <div className="channel-icon">
                <Briefcase size={32} />
              </div>
              <h3>Entrepreneur Hub</h3>
              <p>Practical tips, resources, and inspiration for entrepreneurs and small business owners in Nigeria.</p>
              <ul className="channel-features">
                <li>Business tips and strategies</li>
                <li>Success stories</li>
                <li>Funding opportunities</li>
                <li>Networking events</li>
              </ul>
              <a href="https://wa.me/1234567890?text=Join%20Entrepreneur%20Hub%20Channel" className="button" target="_blank" rel="noopener noreferrer">
                Join Now <MessageSquare size={16} />
              </a>
            </motion.div>

            <motion.div className="channel-card" variants={fadeIn}>
              <div className="channel-icon">
                <Newspaper size={32} />
              </div>
              <h3>Market Trends</h3>
              <p>Stay informed about market trends, economic news, and financial insights relevant to the Nigerian market.</p>
              <ul className="channel-features">
                <li>Daily market updates</li>
                <li>Economic news analysis</li>
                <li>Investment opportunities</li>
                <li>Financial tips and advice</li>
              </ul>
              <a href="https://wa.me/1234567890?text=Join%20Market%20Trends%20Channel" className="button" target="_blank" rel="noopener noreferrer">
                Join Now <MessageSquare size={16} />
              </a>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Romance Channels Section */}
      <section id="romance" className="tv-channels-section section" ref={romanceRef}>
        <div className="container">
          <motion.div
            className="section-header"
            initial="hidden"
            animate={romanceInView ? "visible" : "hidden"}
            variants={fadeIn}
          >
            <h2>Romance Channels</h2>
            <div className="section-divider"></div>
            <p>Explore relationship advice, love stories, and romantic content</p>
          </motion.div>

          <motion.div
            className="channels-grid"
            initial="hidden"
            animate={romanceInView ? "visible" : "hidden"}
            variants={staggerContainer}
          >
            <motion.div className="channel-card" variants={fadeIn}>
              <div className="channel-icon">
                <Heart size={32} />
              </div>
              <h3>Love & Relationships</h3>
              <p>Relationship advice, dating tips, and insights to help you navigate love and relationships.</p>
              <ul className="channel-features">
                <li>Relationship advice</li>
                <li>Dating tips</li>
                <li>Love stories</li>
                <li>Q&A with relationship experts</li>
              </ul>
              <a href="https://wa.me/1234567890?text=Join%20Love%20Relationships%20Channel" className="button" target="_blank" rel="noopener noreferrer">
                Join Now <MessageSquare size={16} />
              </a>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Premium Subscription Section */}
      <section className="premium-section section" ref={premiumRef}>
        <div className="container">
          <motion.div
            className="premium-container"
            initial="hidden"
            animate={premiumInView ? "visible" : "hidden"}
            variants={fadeIn}
          >
            <div className="premium-content">
              <h2>Upgrade to Premium</h2>
              <p>Get an ad-free experience and exclusive content with our premium subscription</p>
              <ul className="premium-features">
                <li>Ad-free experience</li>
                <li>Exclusive premium content</li>
                <li>Early access to updates</li>
                <li>Direct access to content creators</li>
                <li>Special promotions and offers</li>
              </ul>
              <div className="premium-price">
                <span className="price">₦1,000</span>
                <span className="period">per month</span>
              </div>
              <a href="/premium-subscription" className="button">
                <Star size={16} /> Upgrade Now <Star size={16} />
              </a>
            </div>
          </motion.div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="faq-section section" ref={faqRef}>
        <div className="container">
          <motion.div
            className="section-header"
            initial="hidden"
            animate={faqInView ? "visible" : "hidden"}
            variants={fadeIn}
          >
            <h2>Frequently Asked Questions</h2>
            <div className="section-divider"></div>
          </motion.div>

          <motion.div
            className="faq-container"
            initial="hidden"
            animate={faqInView ? "visible" : "hidden"}
            variants={staggerContainer}
          >
            {[
              {
                question: "How often will I receive updates?",
                answer: "Most of our channels send daily updates. Some specialized channels may send updates 2-3 times per week. You can expect regular content without overwhelming your WhatsApp.",
                icon: <AlertCircle size={20} />
              },
              {
                question: "Is there a limit to how many channels I can join?",
                answer: "No, you can join as many channels as you like. However, we recommend selecting channels that truly interest you to avoid information overload.",
                icon: <Users size={20} />
              },
              {
                question: "How do I unsubscribe from a channel?",
                answer: "You can unsubscribe at any time by sending \"STOP\" or \"UNSUBSCRIBE\" to the channel. You'll be removed immediately with no questions asked.",
                icon: <MessageSquare size={20} />
              },
              {
                question: "What's the difference between free and premium subscriptions?",
                answer: "Free subscriptions give you access to regular channel updates. Premium subscriptions offer an ad-free experience, exclusive content, early access to updates, and direct communication with content creators.",
                icon: <Star size={20} />
              },
              {
                question: "Is my personal information secure?",
                answer: "Yes, we take privacy seriously. Your phone number and personal information are never shared with third parties. We use WhatsApp's end-to-end encryption for all communications.",
                icon: <Check size={20} />
              }
            ].map((faq, index) => (
              <motion.div
                key={index}
                className={`faq-item ${activeFaq === index ? 'active' : ''}`}
                variants={fadeIn}
                onClick={() => toggleFaq(index)}
              >
                <div className="faq-question">
                  <div className="faq-icon">{faq.icon}</div>
                  <h3>{faq.question}</h3>
                  <ChevronDown
                    size={20}
                    className={`faq-arrow ${activeFaq === index ? 'rotate' : ''}`}
                  />
                </div>
                <AnimatePresence>
                  {activeFaq === index && (
                    <motion.div
                      className="faq-answer"
                      initial={{ height: 0, opacity: 0 }}
                      animate={{ height: 'auto', opacity: 1 }}
                      exit={{ height: 0, opacity: 0 }}
                      transition={{ duration: 0.3 }}
                    >
                      <p>{faq.answer}</p>
                    </motion.div>
                  )}
                </AnimatePresence>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default JoinTvPage;