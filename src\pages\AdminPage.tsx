import { useState } from 'react';
import BlogPostCreator from '../components/admin/BlogPostCreator';
import '../styles/admin.css';

const AdminPage = () => {
  const [activeTab, setActiveTab] = useState('create-post');

  return (
    <div className="admin-page">
      <div className="container">
        <div className="admin-header">
          <h1>Trex Media Admin</h1>
          <p>Manage your blog content and website</p>
        </div>

        <div className="admin-tabs">
          <button
            className={`tab-button ${activeTab === 'create-post' ? 'active' : ''}`}
            onClick={() => setActiveTab('create-post')}
          >
            Create Blog Post
          </button>
          <button
            className={`tab-button ${activeTab === 'manage-posts' ? 'active' : ''}`}
            onClick={() => setActiveTab('manage-posts')}
          >
            Manage Posts
          </button>
        </div>

        <div className="admin-content">
          {activeTab === 'create-post' && (
            <div className="tab-content">
              <BlogPostCreator />
            </div>
          )}

          {activeTab === 'manage-posts' && (
            <div className="tab-content">
              <div className="manage-posts-section">
                <h2>Manage Blog Posts</h2>
                <p>
                  To manage existing blog posts, edit content, or delete posts, 
                  please use the Sanity Studio at:{' '}
                  <a 
                    href="http://localhost:3333" 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="sanity-link"
                  >
                    http://localhost:3333
                  </a>
                </p>
                <div className="sanity-features">
                  <h3>What you can do in Sanity Studio:</h3>
                  <ul>
                    <li>✏️ Edit existing blog posts</li>
                    <li>🗑️ Delete posts</li>
                    <li>📝 Manage authors and categories</li>
                    <li>🖼️ Upload and manage images</li>
                    <li>📊 View content analytics</li>
                    <li>🔄 Publish/unpublish posts</li>
                  </ul>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AdminPage;
