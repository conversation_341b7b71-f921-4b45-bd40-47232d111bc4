/* Global CSS Variables */
:root {
  /* Brand Colors */
  --color-primary: #7c3aed; /* Improved purple */
  --color-primary-light: #a78bfa;
  --color-primary-dark: #5b21b6;
  --color-secondary: #10b981; /* Teal accent color */
  --color-secondary-light: #34d399;
  --color-secondary-dark: #059669;
  --color-black: #121212;
  --color-white: #ffffff;
  --color-gray-50: #fafafa;
  --color-gray-100: #f4f4f5;
  --color-gray-200: #e4e4e7;
  --color-gray-300: #d4d4d8;
  --color-gray-400: #a1a1aa;
  --color-gray-500: #71717a;
  --color-gray-600: #52525b;
  --color-gray-700: #3f3f46;
  --color-gray-800: #27272a;
  --color-gray-900: #18181b;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* Typography */
  --font-heading: 'Montserrat', system-ui, sans-serif;
  --font-body: 'Inter', 'Open Sans', system-ui, sans-serif;

  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  --spacing-3xl: 4rem;

  /* Border Radius */
  --radius-sm: 0.25rem;
  --radius-md: 0.5rem;
  --radius-lg: 1rem;
  --radius-xl: 1.5rem;
  --radius-full: 9999px;

  /* Transitions */
  --transition-fast: 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Dark Mode Variables */
[data-theme="dark"] {
  --color-primary: #8b5cf6;
  --color-primary-light: #a78bfa;
  --color-primary-dark: #7c3aed;
  --color-secondary: #10b981;
  --color-secondary-light: #34d399;
  --color-secondary-dark: #059669;
  --color-black: #f9fafb;
  --color-white: #111827;
  --color-gray-50: #18181b;
  --color-gray-100: #27272a;
  --color-gray-200: #3f3f46;
  --color-gray-300: #52525b;
  --color-gray-400: #71717a;
  --color-gray-500: #a1a1aa;
  --color-gray-600: #d4d4d8;
  --color-gray-700: #e4e4e7;
  --color-gray-800: #f4f4f5;
  --color-gray-900: #fafafa;
}

/* Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  height: 100%;
  width: 100%;
  overflow-x: hidden;
}

body {
  font-family: var(--font-body);
  color: var(--color-black);
  background-color: var(--color-white);
  line-height: 1.6;
  min-height: 100vh;
  width: 100vw;
  max-width: 100%;
  overflow-x: hidden;
  transition: background-color var(--transition-normal), color var(--transition-normal);
  margin: 0;
  padding: 0;
  display: flex;
  flex-direction: column;
}

h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-heading);
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: var(--spacing-md);
  color: var(--color-black);
  transition: color var(--transition-normal);
}

h1 {
  font-size: 3rem;
}

h2 {
  font-size: 2.5rem;
}

h3 {
  font-size: 2rem;
}

h4 {
  font-size: 1.5rem;
}

p {
  margin-bottom: var(--spacing-md);
}

a {
  color: var(--color-primary);
  text-decoration: none;
  transition: color var(--transition-fast);
}

a:hover {
  color: var(--color-primary-light);
}

img {
  max-width: 100%;
  height: auto;
}

button, .button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  background-color: var(--color-primary);
  color: var(--color-white);
  font-family: var(--font-body);
  font-weight: 600;
  padding: 0.75rem 1.5rem;
  border-radius: var(--radius-md);
  border: none;
  cursor: pointer;
  transition: all var(--transition-fast);
  box-shadow: var(--shadow-sm);
  position: relative;
  overflow: hidden;
  z-index: 1;
}

button:hover, .button:hover {
  background-color: var(--color-primary-light);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

button:active, .button:active {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

.button-secondary {
  background-color: transparent;
  color: var(--color-primary);
  border: 2px solid var(--color-primary);
  box-shadow: none;
}

.button-secondary:hover {
  background-color: var(--color-primary);
  color: var(--color-white);
  box-shadow: var(--shadow-md);
}

.button-accent {
  background-color: var(--color-secondary);
  color: var(--color-white);
}

.button-accent:hover {
  background-color: var(--color-secondary-light);
}

.button-sm {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
}

.button-lg {
  padding: 1rem 2rem;
  font-size: 1.125rem;
}

.container {
  width: 100%;
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
  box-sizing: border-box;
}

#root {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  width: 100vw;
  max-width: 100%;
}

.section {
  padding: var(--spacing-3xl) 0;
  position: relative;
  width: 100%;
  box-sizing: border-box;
}

/* Utility Classes */
.text-center {
  text-align: center;
}

.text-primary {
  color: var(--color-primary);
}

.text-secondary {
  color: var(--color-secondary);
}

.bg-primary {
  background-color: var(--color-primary);
  color: var(--color-white);
}

.bg-secondary {
  background-color: var(--color-secondary);
  color: var(--color-white);
}

.bg-light {
  background-color: var(--color-gray-50);
}

.bg-dark {
  background-color: var(--color-gray-900);
  color: var(--color-white);
}

.shadow-hover {
  transition: box-shadow var(--transition-normal), transform var(--transition-normal);
}

.shadow-hover:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-5px);
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { transform: translateY(20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.animate-fadeIn {
  animation: fadeIn 0.5s ease forwards;
}

.animate-slideUp {
  animation: slideUp 0.5s ease forwards;
}

.animate-pulse {
  animation: pulse 2s infinite;
}

/* Responsive Styles */
@media (max-width: 768px) {
  h1 {
    font-size: 2.5rem;
  }

  h2 {
    font-size: 2rem;
  }

  h3 {
    font-size: 1.75rem;
  }

  .section {
    padding: var(--spacing-2xl) 0;
  }
}

@media (max-width: 480px) {
  h1 {
    font-size: 2rem;
  }

  h2 {
    font-size: 1.75rem;
  }

  h3 {
    font-size: 1.5rem;
  }

  .container {
    padding: 0 var(--spacing-md);
  }
}