import { useRef } from 'react';
import { motion, useInView } from 'framer-motion';
import { Mail, Phone, MapPin, Clock, Send, MessageSquare, Facebook, Twitter, Instagram, Youtube } from 'lucide-react';

// Animation variants
const fadeIn = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0, transition: { duration: 0.6 } }
};

const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2
    }
  }
};

const ContactPage = () => {
  // Refs for scroll animations
  const contactInfoRef = useRef<HTMLDivElement>(null);
  const contactFormRef = useRef<HTMLDivElement>(null);
  const faqRef = useRef<HTMLDivElement>(null);
  
  // Check if sections are in view
  const contactInfoInView = useInView(contactInfoRef, { once: true, amount: 0.3 });
  const contactFormInView = useInView(contactFormRef, { once: true, amount: 0.3 });
  const faqInView = useInView(faqRef, { once: true, amount: 0.3 });

  return (
    <div className="contact-page">
      {/* Hero Section */}
      <section className="page-hero">
        <div className="container">
          <motion.div 
            className="page-hero-content"
            initial="hidden"
            animate="visible"
            variants={staggerContainer}
          >
            <motion.h1 variants={fadeIn}>Contact Us</motion.h1>
            <motion.p variants={fadeIn}>
              Have questions or want to work with us? Reach out to our team and we'll 
              get back to you as soon as possible.
            </motion.p>
          </motion.div>
        </div>
      </section>

      {/* Contact Information Section */}
      <section className="contact-info-section section" ref={contactInfoRef}>
        <div className="container">
          <motion.div 
            className="section-header"
            initial="hidden"
            animate={contactInfoInView ? "visible" : "hidden"}
            variants={fadeIn}
          >
            <h2>Get In Touch</h2>
            <div className="section-divider"></div>
            <p>Multiple ways to connect with our team</p>
          </motion.div>
          
          <motion.div 
            className="contact-info-grid"
            initial="hidden"
            animate={contactInfoInView ? "visible" : "hidden"}
            variants={staggerContainer}
          >
            <motion.div className="contact-info-card" variants={fadeIn}>
              <div className="contact-icon">
                <Mail size={32} />
              </div>
              <h3>Email Us</h3>
              <p>For general inquiries:</p>
              <a href="mailto:<EMAIL>"><EMAIL></a>
              <p>For business opportunities:</p>
              <a href="mailto:<EMAIL>"><EMAIL></a>
            </motion.div>
            
            <motion.div className="contact-info-card" variants={fadeIn}>
              <div className="contact-icon">
                <Phone size={32} />
              </div>
              <h3>Call Us</h3>
              <p>Customer Support:</p>
              <a href="tel:+2341234567890">+234 ************</a>
              <p>Business Line:</p>
              <a href="tel:+2349876543210">+234 ************</a>
            </motion.div>
            
            <motion.div className="contact-info-card" variants={fadeIn}>
              <div className="contact-icon">
                <MapPin size={32} />
              </div>
              <h3>Visit Us</h3>
              <p>Trex Media Headquarters</p>
              <address>
                123 Victoria Island Way<br />
                Lagos, Nigeria
              </address>
            </motion.div>
            
            <motion.div className="contact-info-card" variants={fadeIn}>
              <div className="contact-icon">
                <Clock size={32} />
              </div>
              <h3>Business Hours</h3>
              <p>Monday - Friday:</p>
              <p>9:00 AM - 6:00 PM</p>
              <p>Saturday:</p>
              <p>10:00 AM - 2:00 PM</p>
            </motion.div>
          </motion.div>

          <motion.div 
            className="social-media-container"
            initial="hidden"
            animate={contactInfoInView ? "visible" : "hidden"}
            variants={fadeIn}
          >
            <h3>Connect With Us</h3>
            <div className="social-media-links">
              <a href="https://facebook.com" target="_blank" rel="noopener noreferrer" aria-label="Facebook">
                <Facebook size={24} />
              </a>
              <a href="https://twitter.com" target="_blank" rel="noopener noreferrer" aria-label="Twitter">
                <Twitter size={24} />
              </a>
              <a href="https://instagram.com" target="_blank" rel="noopener noreferrer" aria-label="Instagram">
                <Instagram size={24} />
              </a>
              <a href="https://youtube.com" target="_blank" rel="noopener noreferrer" aria-label="Youtube">
                <Youtube size={24} />
              </a>
              <a href="https://wa.me/1234567890" target="_blank" rel="noopener noreferrer" aria-label="WhatsApp">
                <MessageSquare size={24} />
              </a>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Contact Form Section */}
      <section className="contact-form-section section alternate-bg" ref={contactFormRef}>
        <div className="container">
          <motion.div 
            className="section-header"
            initial="hidden"
            animate={contactFormInView ? "visible" : "hidden"}
            variants={fadeIn}
          >
            <h2>Send Us a Message</h2>
            <div className="section-divider"></div>
            <p>Fill out the form below and we'll get back to you as soon as possible</p>
          </motion.div>
          
          <motion.div 
            className="contact-form-container"
            initial="hidden"
            animate={contactFormInView ? "visible" : "hidden"}
            variants={fadeIn}
          >
            <form className="contact-form">
              <div className="form-row">
                <div className="form-group">
                  <label htmlFor="name">Full Name</label>
                  <input type="text" id="name" name="name" required />
                </div>
                
                <div className="form-group">
                  <label htmlFor="email">Email Address</label>
                  <input type="email" id="email" name="email" required />
                </div>
              </div>
              
              <div className="form-row">
                <div className="form-group">
                  <label htmlFor="phone">Phone Number</label>
                  <input type="tel" id="phone" name="phone" />
                </div>
                
                <div className="form-group">
                  <label htmlFor="subject">Subject</label>
                  <input type="text" id="subject" name="subject" required />
                </div>
              </div>
              
              <div className="form-group">
                <label htmlFor="message">Message</label>
                <textarea id="message" name="message" rows={6} required></textarea>
              </div>
              
              <div className="form-group checkbox-group">
                <input type="checkbox" id="subscribe" name="subscribe" />
                <label htmlFor="subscribe">Subscribe to our newsletter for updates and promotions</label>
              </div>
              
              <button type="submit" className="button">
                Send Message <Send size={16} />
              </button>
            </form>
          </motion.div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="faq-section section" ref={faqRef}>
        <div className="container">
          <motion.div 
            className="section-header"
            initial="hidden"
            animate={faqInView ? "visible" : "hidden"}
            variants={fadeIn}
          >
            <h2>Frequently Asked Questions</h2>
            <div className="section-divider"></div>
          </motion.div>
          
          <motion.div 
            className="faq-container"
            initial="hidden"
            animate={faqInView ? "visible" : "hidden"}
            variants={staggerContainer}
          >
            <motion.div className="faq-item" variants={fadeIn}>
              <h3>How quickly can I expect a response?</h3>
              <p>
                We typically respond to all inquiries within 24-48 hours during business days. 
                For urgent matters, please call our customer support line.
              </p>
            </motion.div>
            
            <motion.div className="faq-item" variants={fadeIn}>
              <h3>I'm interested in advertising on your platform. Who should I contact?</h3>
              <p>
                For advertising and promotion inquiries, <NAME_EMAIL> 
                or visit our <a href="/promote">Promote</a> page for more information about our packages.
              </p>
            </motion.div>
            
            <motion.div className="faq-item" variants={fadeIn}>
              <h3>How can I join your WhatsApp TV channels?</h3>
              <p>
                You can join our WhatsApp TV channels by visiting our <a href="/join-tv">Join TV</a> page 
                and clicking on the join button for your preferred channel.
              </p>
            </motion.div>
            
            <motion.div className="faq-item" variants={fadeIn}>
              <h3>Do you offer content creation services for businesses?</h3>
              <p>
                Yes, we offer professional content creation services for businesses. 
                Please check our <a href="/services">Services</a> page for more details or 
                contact us directly to discuss your specific needs.
              </p>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Map Section */}
      <section className="map-section">
        <div className="map-container">
          {/* This would be replaced with an actual map integration */}
          <div className="map-placeholder">
            <p>Google Maps Integration Here</p>
          </div>
        </div>
      </section>
    </div>
  );
};

export default ContactPage;