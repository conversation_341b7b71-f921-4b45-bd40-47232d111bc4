/* Join TV Page Styles */

/* Hero Section */
.join-tv-page .page-hero {
  background: linear-gradient(135deg, var(--color-primary-dark) 0%, var(--color-primary) 100%);
  color: var(--color-white);
  padding: var(--spacing-3xl) 0;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.join-tv-page .page-hero::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  z-index: 1;
}

.join-tv-page .page-hero::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-image: url('/images/join-tv-pattern.svg');
  background-size: cover;
  opacity: 0.1;
  z-index: 1;
}

.join-tv-page .page-hero-content {
  position: relative;
  z-index: 2;
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
}

.join-tv-page .page-hero-content h1 {
  color: var(--color-white);
  font-size: 3.5rem;
  margin-bottom: var(--spacing-md);
  position: relative;
  display: inline-block;
}

.join-tv-page .page-hero-content h1::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background-color: var(--color-secondary);
  border-radius: var(--radius-full);
}

.join-tv-page .page-hero-content p {
  font-size: 1.2rem;
  opacity: 0.9;
  max-width: 600px;
  margin: 0 auto;
}

/* How It Works Section */
.how-it-works {
  position: relative;
  background-color: var(--color-white);
}

.how-it-works::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at 10% 20%, rgba(124, 58, 237, 0.05) 0%, transparent 70%);
  z-index: 0;
}

.how-it-works .container {
  position: relative;
  z-index: 1;
}

.steps-container {
  display: flex;
  justify-content: space-between;
  gap: var(--spacing-xl);
  margin-top: var(--spacing-2xl);
  position: relative;
}

.steps-container::before {
  content: '';
  position: absolute;
  top: 40px;
  left: 60px;
  right: 60px;
  height: 2px;
  background: linear-gradient(to right, var(--color-primary-light), var(--color-secondary));
  z-index: 0;
}

.step-card {
  background-color: var(--color-white);
  border-radius: var(--radius-lg);
  padding: var(--spacing-xl);
  box-shadow: var(--shadow-md);
  text-align: center;
  flex: 1;
  position: relative;
  z-index: 1;
  transition: all var(--transition-normal);
  border: 1px solid var(--color-gray-200);
}

.step-card:hover {
  transform: translateY(-10px);
  box-shadow: var(--shadow-lg);
  border-color: var(--color-primary-light);
}

.step-number {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
  color: var(--color-white);
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  font-weight: 700;
  margin: 0 auto var(--spacing-lg);
  position: relative;
  box-shadow: var(--shadow-md);
}

.step-card h3 {
  font-size: 1.5rem;
  margin-bottom: var(--spacing-md);
  color: var(--color-primary-dark);
}

.step-card p {
  color: var(--color-gray-600);
}

/* TV Channels Section */
.tv-channels-section {
  position: relative;
  padding: var(--spacing-3xl) 0;
}

.tv-channels-section.alternate-bg {
  background-color: var(--color-gray-50);
}

.tv-channels-section.alternate-bg::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url('/images/dots-pattern.svg');
  opacity: 0.4;
  z-index: 0;
}

.tv-channels-section .container {
  position: relative;
  z-index: 1;
}

.channels-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: var(--spacing-xl);
  margin-top: var(--spacing-xl);
}

.channel-card {
  background-color: var(--color-white);
  border-radius: var(--radius-lg);
  padding: var(--spacing-xl);
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
  border: 1px solid var(--color-gray-200);
  position: relative;
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.channel-card::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 5px;
  background: linear-gradient(to right, var(--color-primary) 0%, var(--color-secondary) 100%);
  transform: scaleX(0);
  transform-origin: left;
  transition: transform var(--transition-normal);
}

.channel-card:hover {
  transform: translateY(-10px);
  box-shadow: var(--shadow-xl);
  border-color: var(--color-primary-light);
}

.channel-card:hover::after {
  transform: scaleX(1);
}

.channel-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, rgba(124, 58, 237, 0.1) 0%, rgba(16, 185, 129, 0.1) 100%);
  color: var(--color-primary);
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--spacing-lg);
  position: relative;
  transition: all var(--transition-normal);
}

.channel-icon::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: var(--radius-full);
  border: 2px dashed var(--color-primary-light);
  opacity: 0;
  transform: scale(1.2);
  transition: all var(--transition-normal);
  z-index: -1;
}

.channel-card:hover .channel-icon {
  transform: scale(1.1);
  background: linear-gradient(135deg, rgba(124, 58, 237, 0.2) 0%, rgba(16, 185, 129, 0.2) 100%);
}

.channel-card:hover .channel-icon::after {
  opacity: 1;
  transform: scale(1.1);
  animation: spin 10s linear infinite;
}

@keyframes spin {
  from { transform: scale(1.1) rotate(0deg); }
  to { transform: scale(1.1) rotate(360deg); }
}

.channel-card h3 {
  font-size: 1.5rem;
  margin-bottom: var(--spacing-md);
  color: var(--color-primary-dark);
}

.channel-card p {
  color: var(--color-gray-600);
  margin-bottom: var(--spacing-lg);
}

.channel-features {
  list-style: none;
  padding: 0;
  margin: 0 0 var(--spacing-xl) 0;
  flex: 1;
}

.channel-features li {
  position: relative;
  padding-left: 1.5rem;
  margin-bottom: var(--spacing-sm);
  color: var(--color-gray-700);
}

.channel-features li::before {
  content: '✓';
  position: absolute;
  left: 0;
  color: var(--color-secondary);
  font-weight: bold;
}

.channel-card .button {
  margin-top: auto;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  transition: all var(--transition-normal);
}

.channel-card .button svg {
  transition: transform var(--transition-normal);
}

.channel-card .button:hover svg {
  transform: translateX(5px);
}

/* Premium Section */
.premium-section {
  background: linear-gradient(135deg, var(--color-primary-dark) 0%, var(--color-primary) 100%);
  color: var(--color-white);
  position: relative;
  overflow: hidden;
}

.premium-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at 90% 10%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
  z-index: 1;
}

.premium-container {
  position: relative;
  z-index: 2;
  background-color: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: var(--radius-lg);
  padding: var(--spacing-2xl);
  box-shadow: var(--shadow-lg);
  border: 1px solid rgba(255, 255, 255, 0.2);
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
}

.premium-content h2 {
  color: var(--color-white);
  margin-bottom: var(--spacing-md);
  position: relative;
  display: inline-block;
}

.premium-content h2::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background-color: var(--color-secondary);
  border-radius: var(--radius-full);
}

.premium-content p {
  font-size: 1.1rem;
  margin-bottom: var(--spacing-xl);
  opacity: 0.9;
}

.premium-features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
  padding: 0;
  list-style: none;
}

.premium-features li {
  position: relative;
  padding-left: 1.5rem;
  text-align: left;
  font-size: 1.1rem;
}

.premium-features li::before {
  content: '✓';
  position: absolute;
  left: 0;
  color: var(--color-secondary);
  font-weight: bold;
}

.premium-price {
  margin-bottom: var(--spacing-xl);
  display: inline-block;
  background-color: rgba(255, 255, 255, 0.1);
  padding: var(--spacing-md) var(--spacing-xl);
  border-radius: var(--radius-full);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.price {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--color-white);
}

.period {
  font-size: 1rem;
  opacity: 0.8;
  margin-left: var(--spacing-xs);
}

.premium-container .button {
  background-color: var(--color-secondary);
  color: var(--color-white);
  padding: var(--spacing-md) var(--spacing-2xl);
  font-size: 1.1rem;
  border-radius: var(--radius-full);
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
}

.premium-container .button:hover {
  background-color: var(--color-secondary-light);
  transform: translateY(-5px);
  box-shadow: var(--shadow-lg);
}

/* FAQ Section */
.faq-section {
  background: linear-gradient(135deg, var(--color-primary-dark) 0%, var(--color-primary) 100%);
  position: relative;
  color: var(--color-white);
  padding-top: var(--spacing-3xl);
  padding-bottom: var(--spacing-3xl);
}

.faq-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at 10% 90%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
  z-index: 0;
}

.faq-section .container {
  position: relative;
  z-index: 1;
}

.faq-section .section-header h2 {
  color: var(--color-white);
  position: relative;
  display: inline-block;
}

.faq-section .section-header h2::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background-color: var(--color-secondary);
  border-radius: var(--radius-full);
}

.faq-section .section-divider {
  background-color: var(--color-white);
  opacity: 0.2;
}

.faq-container {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-xl);
  margin-top: var(--spacing-xl);
}

.faq-item {
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: var(--radius-lg);
  padding: var(--spacing-xl);
  box-shadow: var(--shadow-lg);
  transition: all var(--transition-normal);
  border: 2px solid var(--color-white);
  position: relative;
  overflow: hidden;
  cursor: pointer;
  margin-bottom: var(--spacing-md);
  backdrop-filter: blur(10px);
}

.faq-question {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  position: relative;
}

.faq-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-primary);
  flex-shrink: 0;
  background-color: rgba(124, 58, 237, 0.1);
  width: 36px;
  height: 36px;
  border-radius: var(--radius-full);
}

.faq-arrow {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  transition: transform var(--transition-normal);
  color: var(--color-primary);
}

.faq-arrow.rotate {
  transform: translateY(-50%) rotate(180deg);
}

.faq-answer {
  margin-top: var(--spacing-md);
  padding-left: calc(20px + var(--spacing-md) + var(--spacing-xs));
  overflow: hidden;
  background-color: rgba(124, 58, 237, 0.1);
  padding: var(--spacing-md);
  border-radius: var(--radius-md);
  border-left: 3px solid var(--color-primary);
  margin-left: calc(20px + var(--spacing-md));
  box-shadow: var(--shadow-md);
}

.faq-item::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 0;
  background: linear-gradient(to bottom, var(--color-primary) 0%, var(--color-secondary) 100%);
  transition: height var(--transition-normal);
}

.faq-item.active {
  background-color: var(--color-white);
  border-color: var(--color-primary-light);
  box-shadow: var(--shadow-lg);
  transform: translateY(-5px);
}

.faq-item:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-xl);
  border-color: var(--color-secondary);
  background-color: rgba(255, 255, 255, 1);
}

.faq-item:hover::after {
  height: 100%;
}

.faq-item h3 {
  font-size: 1.2rem;
  margin-bottom: 0;
  color: var(--color-primary-dark);
  transition: all var(--transition-normal);
  flex: 1;
  font-weight: 700;
}

.faq-item.active h3 {
  color: var(--color-primary);
}

.faq-item p {
  color: var(--color-gray-600);
  transition: all var(--transition-normal);
  margin-bottom: 0;
}

/* Responsive Styles */
@media (max-width: 1024px) {
  .steps-container {
    flex-direction: column;
    gap: var(--spacing-2xl);
  }

  .steps-container::before {
    display: none;
  }

  .step-card {
    max-width: 500px;
    margin: 0 auto;
  }

  .faq-container {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .join-tv-page .page-hero-content h1 {
    font-size: 2.5rem;
  }

  .premium-features {
    grid-template-columns: 1fr;
  }

  .channels-grid {
    grid-template-columns: 1fr;
  }

  .channel-card {
    max-width: 500px;
    margin: 0 auto;
  }
}

@media (max-width: 480px) {
  .join-tv-page .page-hero-content h1 {
    font-size: 2rem;
  }

  .step-number {
    width: 60px;
    height: 60px;
    font-size: 1.5rem;
  }

  .premium-container {
    padding: var(--spacing-xl);
  }

  .price {
    font-size: 2rem;
  }
}

/* Dark Mode Adjustments */
[data-theme="dark"] .step-card,
[data-theme="dark"] .channel-card,
[data-theme="dark"] .faq-item {
  background-color: rgba(39, 39, 42, 0.9);
  border-color: var(--color-gray-700);
  color: var(--color-white);
}

[data-theme="dark"] .step-card p,
[data-theme="dark"] .channel-card p,
[data-theme="dark"] .faq-item p {
  color: var(--color-gray-400);
}

[data-theme="dark"] .channel-features li,
[data-theme="dark"] .premium-features li {
  color: var(--color-gray-300);
}

[data-theme="dark"] .tv-channels-section.alternate-bg {
  background-color: var(--color-gray-900);
}

[data-theme="dark"] .how-it-works,
[data-theme="dark"] .faq-section {
  background: linear-gradient(135deg, var(--color-gray-900) 0%, var(--color-primary-dark) 100%);
}
