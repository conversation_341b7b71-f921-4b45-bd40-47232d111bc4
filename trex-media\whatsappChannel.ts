export default {
    name: 'whatsappChannel',
    title: 'WhatsApp TV Channel',
    type: 'document',
    fields: [
      {
        name: 'title',
        title: 'Channel Name',
        type: 'string',
        validation: Rule => Rule.required()
      },
      {
        name: 'description',
        title: 'Description',
        type: 'text',
        rows: 3
      },
      {
        name: 'image',
        title: 'Channel Image',
        type: 'image',
        options: {
          hotspot: true
        }
      },
      {
        name: 'category',
        title: 'Category',
        type: 'reference',
        to: [{type: 'category'}]
      },
      {
        name: 'subscriberCount',
        title: 'Subscriber Count',
        type: 'number',
        initialValue: 0,
        readOnly: true
      }
    ]
  }