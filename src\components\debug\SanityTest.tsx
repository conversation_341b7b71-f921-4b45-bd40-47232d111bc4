import { useState, useEffect } from 'react';
import { client } from '../../lib/sanity';

const SanityTest = () => {
  const [posts, setPosts] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchPosts = async () => {
      try {
        console.log('🧪 SanityTest: Starting fetch...');
        setLoading(true);
        
        // Simple query first
        const result = await client.fetch(`*[_type == "post"]{
          _id,
          title,
          publishedAt,
          "author": author->name
        }`);
        
        console.log('🧪 SanityTest: Raw result:', result);
        setPosts(result);
        
      } catch (err) {
        console.error('🧪 SanityTest: Error:', err);
        setError(err instanceof Error ? err.message : 'Unknown error');
      } finally {
        setLoading(false);
      }
    };

    fetchPosts();
  }, []);

  if (loading) {
    return <div style={{ padding: '20px', border: '2px solid blue' }}>
      <h3>🧪 Sanity Test - Loading...</h3>
    </div>;
  }

  if (error) {
    return <div style={{ padding: '20px', border: '2px solid red' }}>
      <h3>🧪 Sanity Test - Error</h3>
      <p>{error}</p>
    </div>;
  }

  return (
    <div style={{ padding: '20px', border: '2px solid green' }}>
      <h3>🧪 Sanity Test - Success!</h3>
      <p>Found {posts.length} posts:</p>
      <ul>
        {posts.map((post) => (
          <li key={post._id}>
            <strong>{post.title}</strong> by {post.author || 'Unknown'} 
            {post.publishedAt && ` (${new Date(post.publishedAt).toLocaleDateString()})`}
          </li>
        ))}
      </ul>
    </div>
  );
};

export default SanityTest;
