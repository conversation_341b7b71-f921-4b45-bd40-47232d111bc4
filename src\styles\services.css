/* Services Page Styles */

/* Services Overview */
.services-overview {
  position: relative;
  background-color: var(--color-white);
  padding-top: var(--spacing-3xl);
  padding-bottom: var(--spacing-3xl);
}

.services-overview::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at 10% 20%, rgba(124, 58, 237, 0.05) 0%, transparent 70%);
  z-index: 0;
}

.services-overview .container {
  position: relative;
  z-index: 1;
}

.services-overview .section-header h2 {
  color: var(--color-primary-dark);
  font-size: 2.2rem;
  font-weight: 800;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  position: relative;
  display: inline-block;
  padding: 0 20px;
}

.services-overview .section-header h2::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background: linear-gradient(to right, var(--color-primary), var(--color-secondary));
  border-radius: var(--radius-full);
}

.services-overview .section-header p {
  color: var(--color-gray-800);
  font-size: 1.2rem;
  font-weight: 600;
  max-width: 800px;
  margin: 0 auto;
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacing-xl);
  margin-top: var(--spacing-xl);
}

/* Service Detail Sections */
.service-detail {
  padding: var(--spacing-3xl) 0;
  position: relative;
  background-color: var(--color-white);
}

.service-detail.alternate-bg {
  background-color: var(--color-gray-50);
}

.service-detail.alternate-bg::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url('/images/dots-pattern.svg');
  opacity: 0.4;
  z-index: 0;
}

.service-detail .container {
  position: relative;
  z-index: 1;
}

.service-detail-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-2xl);
  align-items: center;
}

.service-detail-content.reverse {
  grid-template-columns: 1fr 1fr;
}

/* Content Creation Section */
#content-creation {
  position: relative;
  overflow: hidden;
}

#content-creation::before {
  content: '';
  position: absolute;
  top: -30%;
  right: -30%;
  width: 60%;
  height: 60%;
  background: radial-gradient(circle, rgba(124, 58, 237, 0.05) 0%, transparent 70%);
  border-radius: 50%;
  z-index: 0;
}

.service-detail-text {
  position: relative;
}

.service-detail-text h2 {
  color: var(--color-primary);
  margin-bottom: var(--spacing-md);
  position: relative;
  display: inline-block;
}

.service-detail-text h2::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 0;
  width: 60px;
  height: 3px;
  background-color: var(--color-secondary);
  border-radius: var(--radius-full);
}

.service-features {
  list-style: none;
  padding: 0;
  margin: var(--spacing-lg) 0;
}

.service-features li {
  position: relative;
  padding-left: 1.5rem;
  margin-bottom: var(--spacing-sm);
  color: var(--color-gray-800);
  transition: transform var(--transition-normal);
  font-weight: 500;
}

.service-features li:hover {
  transform: translateX(5px);
}

.service-features li::before {
  content: '✓';
  position: absolute;
  left: 0;
  color: var(--color-secondary);
  font-weight: bold;
}

.service-pricing {
  background-color: var(--color-gray-50);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  margin-top: var(--spacing-xl);
  border-left: 4px solid var(--color-primary);
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
}

.service-pricing:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-lg);
}

.service-pricing h3 {
  color: var(--color-primary);
  margin-bottom: var(--spacing-sm);
  font-size: 1.2rem;
}

.service-pricing p {
  margin-bottom: var(--spacing-xs);
  color: var(--color-gray-800);
  font-weight: 500;
}

.service-detail-image {
  position: relative;
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-lg);
  transition: all var(--transition-normal);
  border: 5px solid var(--color-white);
  height: 100%;
  max-height: 400px;
}

.service-detail-image:hover {
  transform: translateY(-10px) rotate(1deg);
  box-shadow: var(--shadow-xl);
}

.service-detail-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform var(--transition-normal);
}

.service-detail-image:hover img {
  transform: scale(1.05);
}

/* WhatsApp TV Section */
#whatsapp-tv {
  position: relative;
  overflow: hidden;
}

#whatsapp-tv::before {
  content: '';
  position: absolute;
  bottom: -30%;
  left: -30%;
  width: 60%;
  height: 60%;
  background: radial-gradient(circle, rgba(16, 185, 129, 0.05) 0%, transparent 70%);
  border-radius: 50%;
  z-index: 0;
}

#whatsapp-tv .service-pricing {
  border-left: 4px solid var(--color-secondary);
}

#whatsapp-tv .service-pricing h3 {
  color: var(--color-secondary);
}

#whatsapp-tv .service-features li::before {
  color: var(--color-secondary-dark);
}

#whatsapp-tv .service-detail-text h2::after {
  background-color: var(--color-secondary);
}

/* Digital Marketing Section */
#digital-marketing {
  position: relative;
  overflow: hidden;
}

#digital-marketing::before {
  content: '';
  position: absolute;
  top: -30%;
  right: -30%;
  width: 60%;
  height: 60%;
  background: radial-gradient(circle, rgba(124, 58, 237, 0.05) 0%, transparent 70%);
  border-radius: 50%;
  z-index: 0;
}

#digital-marketing .service-pricing {
  background: linear-gradient(135deg, rgba(124, 58, 237, 0.05) 0%, rgba(16, 185, 129, 0.05) 100%);
}

/* Service Cards */
.service-card {
  background-color: var(--color-white);
  border-radius: var(--radius-lg);
  padding: var(--spacing-xl);
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
  border: 1px solid var(--color-gray-300);
  position: relative;
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.service-card::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 5px;
  background: linear-gradient(to right, var(--color-primary) 0%, var(--color-secondary) 100%);
  transform: scaleX(0);
  transform-origin: left;
  transition: transform var(--transition-normal);
}

.service-card:hover {
  transform: translateY(-10px);
  box-shadow: var(--shadow-xl);
  border-color: var(--color-primary-light);
}

.service-card:hover::after {
  transform: scaleX(1);
}

.service-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 70px;
  height: 70px;
  background-color: rgba(124, 58, 237, 0.1);
  color: var(--color-primary);
  border-radius: var(--radius-full);
  margin-bottom: var(--spacing-md);
  position: relative;
  transition: all var(--transition-normal);
}

.service-icon::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: var(--radius-full);
  border: 2px dashed var(--color-primary-light);
  opacity: 0;
  transform: scale(1.2);
  transition: all var(--transition-normal);
  z-index: -1;
}

.service-card:hover .service-icon {
  transform: scale(1.1);
  background: linear-gradient(135deg, rgba(124, 58, 237, 0.2) 0%, rgba(16, 185, 129, 0.2) 100%);
}

.service-card:hover .service-icon::after {
  opacity: 1;
  transform: scale(1.1);
  animation: spin 10s linear infinite;
}

@keyframes spin {
  from { transform: scale(1.1) rotate(0deg); }
  to { transform: scale(1.1) rotate(360deg); }
}

.service-card h3 {
  font-size: 1.5rem;
  margin-bottom: var(--spacing-md);
  color: var(--color-primary-dark);
}

.service-card p {
  color: var(--color-gray-700);
  margin-bottom: var(--spacing-lg);
  flex: 1;
  font-weight: 500;
}

.text-link {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-weight: 600;
  color: var(--color-primary);
  transition: all var(--transition-normal);
  margin-top: auto;
}

.text-link:hover {
  color: var(--color-primary-dark);
  gap: var(--spacing-sm);
}

/* Additional Services */
.additional-services {
  background-color: var(--color-gray-50);
  position: relative;
}

.additional-services::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url('/images/dots-pattern.svg');
  opacity: 0.4;
  z-index: 0;
}

.additional-services .container {
  position: relative;
  z-index: 1;
}

.additional-services .section-header h2 {
  color: var(--color-primary-dark);
  font-size: 2.2rem;
  font-weight: 800;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  position: relative;
  display: inline-block;
  padding: 0 20px;
}

.additional-services .section-header h2::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background: linear-gradient(to right, var(--color-primary), var(--color-secondary));
  border-radius: var(--radius-full);
}

.additional-services .section-header p {
  color: var(--color-gray-800);
  font-size: 1.2rem;
  font-weight: 600;
  max-width: 800px;
  margin: 0 auto;
}

.three-column {
  grid-template-columns: repeat(3, 1fr);
}

/* Responsive Styles */
@media (max-width: 1024px) {
  .services-grid,
  .three-column {
    grid-template-columns: repeat(2, 1fr);
  }

  .service-detail-content,
  .service-detail-content.reverse {
    grid-template-columns: 1fr;
    gap: var(--spacing-xl);
  }

  .service-detail-content.reverse .service-detail-image {
    order: -1;
  }
}

@media (max-width: 768px) {
  .services-grid,
  .three-column {
    grid-template-columns: 1fr;
  }

  .service-detail-image {
    max-height: 300px;
  }
}

/* Dark Mode Adjustments */
[data-theme="dark"] .service-card,
[data-theme="dark"] .service-pricing {
  background-color: var(--color-gray-800);
  border-color: var(--color-gray-700);
}

[data-theme="dark"] .service-card p,
[data-theme="dark"] .service-pricing p,
[data-theme="dark"] .service-features li {
  color: var(--color-gray-400);
}

[data-theme="dark"] .service-detail.alternate-bg {
  background-color: var(--color-gray-900);
}

[data-theme="dark"] .services-overview,
[data-theme="dark"] .additional-services {
  background-color: var(--color-gray-900);
}

[data-theme="dark"] .services-overview .section-header h2,
[data-theme="dark"] .additional-services .section-header h2 {
  color: var(--color-primary-light);
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .services-overview .section-header p,
[data-theme="dark"] .additional-services .section-header p {
  color: var(--color-gray-200);
}

[data-theme="dark"] .services-overview .section-header h2::after,
[data-theme="dark"] .additional-services .section-header h2::after {
  background: linear-gradient(to right, var(--color-primary-light), var(--color-secondary-light));
  opacity: 0.8;
}

[data-theme="dark"] #digital-marketing .service-pricing {
  background: linear-gradient(135deg, rgba(124, 58, 237, 0.1) 0%, rgba(16, 185, 129, 0.1) 100%);
}
