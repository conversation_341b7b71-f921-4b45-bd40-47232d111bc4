import { useState } from 'react';
import { client } from '../../lib/sanity';

interface BlogPostForm {
  title: string;
  content: string;
  category: string;
  author: string;
  isWhatsAppContent: boolean;
  isPremium: boolean;
}

const BlogPostCreator = () => {
  const [formData, setFormData] = useState<BlogPostForm>({
    title: '',
    content: '',
    category: 'Industry Insights',
    author: '<PERSON>',
    isWhatsAppContent: false,
    isPremium: false
  });
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');

  const categories = [
    'Industry Insights',
    'Content Creation',
    'Digital Marketing',
    'Community',
    'Business',
    'Entertainment'
  ];

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setMessage('');

    try {
      // Create slug from title
      const slug = formData.title
        .toLowerCase()
        .replace(/[^a-z0-9]+/g, '-')
        .replace(/(^-|-$)/g, '');

      // Create or get author
      let authorId;
      const existingAuthor = await client.fetch(`*[_type == "author" && name == $name][0]`, { name: formData.author });
      
      if (existingAuthor) {
        authorId = existingAuthor._id;
      } else {
        const newAuthor = await client.create({
          _type: 'author',
          name: formData.author,
          slug: { current: formData.author.toLowerCase().replace(/\s+/g, '-') },
          bio: [
            {
              _type: 'block',
              children: [
                {
                  _type: 'span',
                  text: `${formData.author} is a content creator at Trex Media.`
                }
              ]
            }
          ]
        });
        authorId = newAuthor._id;
      }

      // Create or get category
      let categoryId;
      const existingCategory = await client.fetch(`*[_type == "category" && title == $title][0]`, { title: formData.category });
      
      if (existingCategory) {
        categoryId = existingCategory._id;
      } else {
        const newCategory = await client.create({
          _type: 'category',
          title: formData.category,
          description: `Articles related to ${formData.category.toLowerCase()}`
        });
        categoryId = newCategory._id;
      }

      // Convert content to block content
      const paragraphs = formData.content.split('\n\n').filter(p => p.trim());
      const body = paragraphs.map(paragraph => ({
        _type: 'block',
        children: [
          {
            _type: 'span',
            text: paragraph.trim()
          }
        ]
      }));

      // Create the blog post
      const post = await client.create({
        _type: 'post',
        title: formData.title,
        slug: { current: slug },
        author: { _type: 'reference', _ref: authorId },
        categories: [{ _type: 'reference', _ref: categoryId }],
        publishedAt: new Date().toISOString(),
        body: body,
        isWhatsAppContent: formData.isWhatsAppContent,
        isPremium: formData.isPremium
      });

      setMessage(`✅ Blog post "${post.title}" created successfully!`);
      setFormData({
        title: '',
        content: '',
        category: 'Industry Insights',
        author: 'Jackson Abetianbe',
        isWhatsAppContent: false,
        isPremium: false
      });
    } catch (error) {
      console.error('Error creating blog post:', error);
      setMessage('❌ Error creating blog post. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    }));
  };

  return (
    <div className="blog-post-creator">
      <h2>Create New Blog Post</h2>
      
      {message && (
        <div className={`message ${message.includes('✅') ? 'success' : 'error'}`}>
          {message}
        </div>
      )}

      <form onSubmit={handleSubmit} className="blog-form">
        <div className="form-group">
          <label htmlFor="title">Title</label>
          <input
            type="text"
            id="title"
            name="title"
            value={formData.title}
            onChange={handleChange}
            required
            placeholder="Enter blog post title"
          />
        </div>

        <div className="form-group">
          <label htmlFor="author">Author</label>
          <input
            type="text"
            id="author"
            name="author"
            value={formData.author}
            onChange={handleChange}
            required
            placeholder="Author name"
          />
        </div>

        <div className="form-group">
          <label htmlFor="category">Category</label>
          <select
            id="category"
            name="category"
            value={formData.category}
            onChange={handleChange}
            required
          >
            {categories.map(cat => (
              <option key={cat} value={cat}>{cat}</option>
            ))}
          </select>
        </div>

        <div className="form-group">
          <label htmlFor="content">Content</label>
          <textarea
            id="content"
            name="content"
            value={formData.content}
            onChange={handleChange}
            required
            rows={10}
            placeholder="Write your blog post content here. Use double line breaks to separate paragraphs."
          />
        </div>

        <div className="form-group checkbox-group">
          <label>
            <input
              type="checkbox"
              name="isWhatsAppContent"
              checked={formData.isWhatsAppContent}
              onChange={handleChange}
            />
            Share on WhatsApp TV
          </label>
        </div>

        <div className="form-group checkbox-group">
          <label>
            <input
              type="checkbox"
              name="isPremium"
              checked={formData.isPremium}
              onChange={handleChange}
            />
            Premium Content
          </label>
        </div>

        <button type="submit" disabled={loading} className="submit-button">
          {loading ? 'Creating...' : 'Create Blog Post'}
        </button>
      </form>
    </div>
  );
};

export default BlogPostCreator;
