import { createClient } from '@sanity/client';

// Create Sanity client (read-only)
const client = createClient({
  projectId: '535k6265',
  dataset: 'production',
  useCdn: true,
  apiVersion: '2024-01-01',
});

async function testConnection() {
  try {
    console.log('🔍 Testing Sanity connection...');
    
    // Test basic connection
    const posts = await client.fetch(`*[_type == "post"]`);
    console.log(`✅ Found ${posts.length} posts in Sanity`);

    if (posts.length > 0) {
      console.log('📝 First post:', posts[0].title);
      console.log('📅 Published:', posts[0].publishedAt);
      console.log('🏷️ Has slug:', !!posts[0].slug);

      // Check all posts for publishedAt
      console.log('\n📋 All posts status:');
      posts.forEach((post, index) => {
        console.log(`${index + 1}. "${post.title}" - Published: ${post.publishedAt ? '✅' : '❌'}`);
      });
    }
    
    // Test the exact query used by the website
    const processedPosts = await client.fetch(`*[_type == "post" && defined(publishedAt)] | order(publishedAt desc) {
      _id,
      title,
      slug,
      publishedAt,
      mainImage,
      "author": author->name,
      "categories": categories[]->title,
      body,
      isWhatsAppContent,
      isPremium
    }`);
    
    console.log(`✅ Processed query found ${processedPosts.length} posts`);
    
    if (processedPosts.length > 0) {
      console.log('📋 Sample processed post:');
      console.log('- Title:', processedPosts[0].title);
      console.log('- Slug:', processedPosts[0].slug?.current);
      console.log('- Author:', processedPosts[0].author);
      console.log('- Categories:', processedPosts[0].categories);
      console.log('- Published:', processedPosts[0].publishedAt);
    }
    
  } catch (error) {
    console.error('❌ Error testing Sanity connection:', error);
  }
}

testConnection();
