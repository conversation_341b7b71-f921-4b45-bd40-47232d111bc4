import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Calendar, User, Clock, ChevronRight } from 'lucide-react';
import { client } from '../../lib/sanity';

const fadeIn = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0, transition: { duration: 0.6 } }
};

const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2
    }
  }
};

const SimpleBlogGrid = () => {
  const [posts, setPosts] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchPosts = async () => {
      try {
        const result = await client.fetch(`*[_type == "post"] | order(_createdAt desc) {
          _id,
          title,
          slug,
          publishedAt,
          "author": author->name,
          "categories": categories[]->title,
          body
        }`);
        
        // Simple processing
        const processedPosts = result.map((post: any) => ({
          id: post._id,
          title: post.title || 'Untitled',
          slug: post.slug?.current || 'no-slug',
          author: post.author || 'Unknown Author',
          date: post.publishedAt ? new Date(post.publishedAt).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
          }) : 'No date',
          category: post.categories?.[0] || 'General',
          excerpt: post.body?.[0]?.children?.[0]?.text?.substring(0, 150) + '...' || 'No excerpt available.',
          image: '/images/blog-1.png',
          readingTime: '5 min read'
        }));
        
        setPosts(processedPosts);
      } catch (error) {
        console.error('Error fetching posts:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchPosts();
  }, []);

  if (loading) {
    return (
      <div className="blog-grid">
        {[1, 2, 3].map(i => (
          <div key={i} className="blog-card">
            <div className="blog-card-image">
              <div className="loading-placeholder"></div>
            </div>
            <div className="blog-card-content">
              <div className="loading-placeholder-text"></div>
              <div className="loading-placeholder-text"></div>
              <div className="loading-placeholder-text"></div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <motion.div
      className="blog-grid"
      initial="hidden"
      animate="visible"
      variants={staggerContainer}
    >
      {posts.length > 0 ? (
        posts.map((post) => (
          <motion.div key={post.id} className="blog-card" variants={fadeIn}>
            <div className="blog-card-image">
              <img src={post.image} alt={post.title} />
              <div className="post-category">{post.category}</div>
            </div>
            <div className="blog-card-content">
              <h3>{post.title}</h3>
              <div className="post-meta">
                <span><Calendar size={14} /> {post.date}</span>
                <span><User size={14} /> {post.author}</span>
              </div>
              <p>{post.excerpt}</p>
              <div className="card-footer">
                <span className="reading-time"><Clock size={14} /> {post.readingTime}</span>
                <a href={`/blog/${post.slug}`} className="text-link">
                  Read More <ChevronRight size={16} />
                </a>
              </div>
            </div>
          </motion.div>
        ))
      ) : (
        <div className="no-results">
          <h3>No articles found</h3>
          <p>No blog posts are available at the moment.</p>
        </div>
      )}
    </motion.div>
  );
};

export default SimpleBlogGrid;
