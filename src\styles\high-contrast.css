/* High Contrast Styles - Focused on text visibility without changing color theme */

/* Global Improvements */
p {
  font-weight: 500;
}

h1, h2, h3, h4, h5, h6 {
  font-weight: 700;
}

/* Service Cards */
.service-card {
  box-shadow: var(--shadow-lg);
}

.service-card h3 {
  font-weight: 700;
}

.service-card p {
  font-weight: 500;
}

/* Service Details */
.service-features li {
  font-weight: 500;
}

.service-pricing {
  box-shadow: var(--shadow-lg);
}

.service-pricing p {
  font-weight: 500;
}

/* FAQ Section */
.faq-item {
  box-shadow: var(--shadow-xl);
}

.faq-item h3 {
  font-weight: 700;
}

.faq-item p {
  font-weight: 500;
  box-shadow: var(--shadow-lg);
}

/* WhatsApp TV Section */
#whatsapp-tv .service-features li {
  font-weight: 500;
}

/* Dark Mode Adjustments */
[data-theme="dark"] p {
  font-weight: 500;
}

[data-theme="dark"] h1,
[data-theme="dark"] h2,
[data-theme="dark"] h3,
[data-theme="dark"] h4,
[data-theme="dark"] h5,
[data-theme="dark"] h6 {
  font-weight: 700;
}

/* Improve visibility in dark mode without changing colors */
[data-theme="dark"] .service-features li,
[data-theme="dark"] .service-pricing p,
[data-theme="dark"] .faq-item p {
  font-weight: 500;
}
