import { useRef } from 'react';
import { motion, useInView } from 'framer-motion';
import { MessageSquare, Users, TrendingUp, BarChart, Globe, Megaphone, Camera, Edit, Video, Music, Heart, Award, Target, ChevronRight } from 'lucide-react';

// Animation variants
const fadeIn = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0, transition: { duration: 0.6 } }
};

const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2
    }
  }
};

const ServicesPage = () => {
  // Refs for scroll animations
  const contentCreationRef = useRef<HTMLDivElement>(null);
  const whatsappTvRef = useRef<HTMLDivElement>(null);
  const digitalMarketingRef = useRef<HTMLDivElement>(null);
  const faqRef = useRef<HTMLDivElement>(null);

  // Check if sections are in view
  const contentCreationInView = useInView(contentCreationRef, { once: true, amount: 0.3 });
  const whatsappTvInView = useInView(whatsappTvRef, { once: true, amount: 0.3 });
  const digitalMarketingInView = useInView(digitalMarketingRef, { once: true, amount: 0.3 });
  const faqInView = useInView(faqRef, { once: true, amount: 0.3 });

  return (
    <div className="services-page">
      {/* Hero Section */}
      <section className="page-hero">
        <div className="container">
          <motion.div
            className="page-hero-content"
            initial="hidden"
            animate="visible"
            variants={staggerContainer}
          >
            <motion.h1 variants={fadeIn}>Our Services</motion.h1>
            <motion.p variants={fadeIn}>
              Discover how Trex Media can help elevate your digital presence with our
              comprehensive suite of services tailored for the Nigerian market.
            </motion.p>
          </motion.div>
        </div>
      </section>

      {/* Services Overview */}
      <section className="services-overview section">
        <div className="container">
          <motion.div
            className="section-header"
            initial="hidden"
            animate="visible"
            variants={fadeIn}
          >
            <h2>What We Offer</h2>
            <div className="section-divider"></div>
            <p>Comprehensive digital media solutions for businesses and individuals</p>
          </motion.div>

          <motion.div
            className="services-grid"
            initial="hidden"
            animate="visible"
            variants={staggerContainer}
          >
            <motion.div className="service-card" variants={fadeIn}>
              <div className="service-icon">
                <MessageSquare size={32} />
              </div>
              <h3>Content Creation</h3>
              <p>Engaging digital content tailored for Nigerian audiences across various platforms. Our creative team crafts compelling stories that resonate with your target audience.</p>
              <a href="#content-creation" className="text-link">Learn more <ChevronRight size={16} /></a>
            </motion.div>

            <motion.div className="service-card" variants={fadeIn}>
              <div className="service-icon">
                <Users size={32} />
              </div>
              <h3>WhatsApp TV</h3>
              <p>Join our niche-specific WhatsApp channels for daily updates and entertainment. Get curated content delivered directly to your phone.</p>
              <a href="#whatsapp-tv" className="text-link">Learn more <ChevronRight size={16} /></a>
            </motion.div>

            <motion.div className="service-card" variants={fadeIn}>
              <div className="service-icon">
                <TrendingUp size={32} />
              </div>
              <h3>Digital Marketing</h3>
              <p>Promote your brand to our engaged audience through our various channels. Reach thousands of potential customers with targeted campaigns.</p>
              <a href="#digital-marketing" className="text-link">Learn more <ChevronRight size={16} /></a>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Content Creation Section */}
      <section id="content-creation" className="service-detail section" ref={contentCreationRef}>
        <div className="container">
          <motion.div
            className="service-detail-content"
            initial="hidden"
            animate={contentCreationInView ? "visible" : "hidden"}
            variants={staggerContainer}
          >
            <motion.div className="service-detail-text" variants={fadeIn}>
              <h2>Content Creation</h2>
              <div className="section-divider"></div>
              <p>
                Our team of creative professionals specializes in producing high-quality,
                engaging content that resonates with Nigerian audiences. We understand the
                local culture, trends, and preferences, allowing us to create content that
                truly connects with your target audience and drives engagement.
              </p>
              <ul className="service-features">
                <li><Camera size={18} /> Social media content creation and management</li>
                <li><Video size={18} /> Video production and editing for all platforms</li>
                <li><Edit size={18} /> Graphic design and visual content that stands out</li>
                <li><MessageSquare size={18} /> Blog and article writing with SEO optimization</li>
                <li><Award size={18} /> Meme and viral content creation that drives engagement</li>
                <li><Music size={18} /> Audio content and podcast production</li>
              </ul>
              <div className="service-pricing">
                <h3>Pricing</h3>
                <p><strong>Basic Package:</strong> ₦50,000 per month</p>
                <p><strong>Standard Package:</strong> ₦100,000 per month</p>
                <p><strong>Premium Package:</strong> ₦200,000 per month</p>
                <p>Custom packages available based on specific needs</p>
              </div>
            </motion.div>
            <motion.div className="service-detail-image" variants={fadeIn}>
              <img src="/images/content-creation.png" alt="Content Creation Services" />
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* WhatsApp TV Section */}
      <section id="whatsapp-tv" className="service-detail section alternate-bg" ref={whatsappTvRef}>
        <div className="container">
          <motion.div
            className="service-detail-content reverse"
            initial="hidden"
            animate={whatsappTvInView ? "visible" : "hidden"}
            variants={staggerContainer}
          >
            <motion.div className="service-detail-image" variants={fadeIn}>
              <img src="/images/whatsapp-tv.png" alt="WhatsApp TV Services" />
            </motion.div>
            <motion.div className="service-detail-text" variants={fadeIn}>
              <h2>WhatsApp TV</h2>
              <div className="section-divider"></div>
              <p>
                Our WhatsApp TV channels deliver curated content directly to subscribers'
                phones. We offer various niche channels including Entertainment, Business,
                and Romance, each providing daily updates, news, and engaging content that keeps
                our audience informed and entertained.
              </p>
              <ul className="service-features">
                <li><MessageSquare size={18} /> Daily content updates delivered to your phone</li>
                <li><Award size={18} /> Exclusive news and information before anyone else</li>
                <li><Users size={18} /> Community engagement with like-minded individuals</li>
                <li><TrendingUp size={18} /> Trending topics and discussions that matter</li>
                <li><Heart size={18} /> Special promotions and offers for subscribers only</li>
                <li><Video size={18} /> Multimedia content including videos and images</li>
              </ul>
              <div className="service-pricing">
                <h3>Subscription</h3>
                <p><strong>Free Tier:</strong> Basic content updates and community access</p>
                <p><strong>Premium:</strong> ₦1,000 per month for ad-free experience and exclusive content</p>
                <p><strong>VIP Access:</strong> ₦2,500 per month for all premium features plus direct creator access</p>
              </div>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Digital Marketing Section */}
      <section id="digital-marketing" className="service-detail section" ref={digitalMarketingRef}>
        <div className="container">
          <motion.div
            className="service-detail-content"
            initial="hidden"
            animate={digitalMarketingInView ? "visible" : "hidden"}
            variants={staggerContainer}
          >
            <motion.div className="service-detail-text" variants={fadeIn}>
              <h2>Digital Marketing</h2>
              <div className="section-divider"></div>
              <p>
                Leverage our extensive network and engaged audience to promote your brand,
                products, or services. We offer comprehensive digital marketing solutions
                designed to increase visibility, engagement, and conversions in the Nigerian market.
                Our data-driven approach ensures maximum ROI for your marketing budget.
              </p>
              <ul className="service-features">
                <li><MessageSquare size={18} /> WhatsApp TV promotions and sponsored content</li>
                <li><Users size={18} /> Social media marketing campaigns across all platforms</li>
                <li><Award size={18} /> Influencer collaborations with top Nigerian creators</li>
                <li><Edit size={18} /> Content marketing strategies tailored to your audience</li>
                <li><BarChart size={18} /> Performance tracking and detailed analytics reports</li>
                <li><Target size={18} /> Targeted advertising to reach your ideal customers</li>
              </ul>
              <div className="service-pricing">
                <h3>Pricing</h3>
                <p><strong>WhatsApp TV Promotions:</strong> Starting from ₦20,000 per post</p>
                <p><strong>Social Media Campaign:</strong> Starting from ₦75,000 per platform</p>
                <p><strong>Full Marketing Campaigns:</strong> Starting from ₦150,000 per month</p>
                <p><strong>Influencer Partnerships:</strong> Starting from ₦50,000 per collaboration</p>
                <p>Custom packages available based on campaign objectives and budget</p>
              </div>
            </motion.div>
            <motion.div className="service-detail-image" variants={fadeIn}>
              <img src="/images/case-study-1.png" alt="Digital Marketing Services" />
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Additional Services */}
      <section className="additional-services section">
        <div className="container">
          <motion.div
            className="section-header"
            initial="hidden"
            animate="visible"
            variants={fadeIn}
          >
            <h2>Additional Services</h2>
            <div className="section-divider"></div>
            <p>Beyond our core offerings, we provide specialized services to meet your unique needs</p>
          </motion.div>

          <motion.div
            className="services-grid three-column"
            initial="hidden"
            animate="visible"
            variants={staggerContainer}
          >
            <motion.div className="service-card" variants={fadeIn}>
              <div className="service-icon">
                <BarChart size={32} />
              </div>
              <h3>Analytics & Insights</h3>
              <p>Data-driven insights to help you understand your audience and optimize your digital strategy.</p>
              <img src="/images/testimonial-1.png" alt="Analytics & Insights" style={{ width: '100%', height: 'auto', marginTop: '15px', borderRadius: '8px' }} />
            </motion.div>

            <motion.div className="service-card" variants={fadeIn}>
              <div className="service-icon">
                <Globe size={32} />
              </div>
              <h3>Website Development</h3>
              <p>Custom website design and development to establish your online presence.</p>
              <img src="/images/hero-image.png" alt="Website Development" style={{ width: '100%', height: 'auto', marginTop: '15px', borderRadius: '8px' }} />
            </motion.div>

            <motion.div className="service-card" variants={fadeIn}>
              <div className="service-icon">
                <Megaphone size={32} />
              </div>
              <h3>PR & Media Relations</h3>
              <p>Strategic public relations services to enhance your brand's reputation and visibility.</p>
              <img src="/images/about-image.png" alt="PR & Media Relations" style={{ width: '100%', height: 'auto', marginTop: '15px', borderRadius: '8px' }} />
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="faq-section section" ref={faqRef}>
        <div className="container">
          <motion.div
            className="section-header"
            initial="hidden"
            animate={faqInView ? "visible" : "hidden"}
            variants={fadeIn}
          >
            <h2>Frequently Asked Questions</h2>
            <div className="section-divider"></div>
          </motion.div>

          <motion.div
            className="faq-container"
            initial="hidden"
            animate={faqInView ? "visible" : "hidden"}
            variants={staggerContainer}
          >
            <motion.div className="faq-item" variants={fadeIn}>
              <h3>How do I join a WhatsApp TV channel?</h3>
              <p>
                You can join our WhatsApp TV channels by visiting the Join TV page and clicking on the
                channel you're interested in. You'll be redirected to WhatsApp where you can start
                receiving updates immediately.
              </p>
            </motion.div>

            <motion.div className="faq-item" variants={fadeIn}>
              <h3>What types of content do you create?</h3>
              <p>
                We create a wide range of content including social media posts, videos, graphics,
                articles, and more. Our content is tailored to Nigerian audiences and designed to
                engage and resonate with your target demographic.
              </p>
            </motion.div>

            <motion.div className="faq-item" variants={fadeIn}>
              <h3>How much does it cost to promote on your platforms?</h3>
              <p>
                Promotion costs vary depending on the platform, duration, and type of promotion.
                WhatsApp TV promotions start from ₦20,000 per post, while comprehensive marketing
                campaigns start from ₦100,000 per month. Contact us for a customized quote.
              </p>
            </motion.div>

            <motion.div className="faq-item" variants={fadeIn}>
              <h3>Do you offer custom packages?</h3>
              <p>
                Yes, we offer custom packages tailored to your specific needs and budget. Contact
                us to discuss your requirements and we'll create a personalized solution for you.
              </p>
            </motion.div>

            <motion.div className="faq-item" variants={fadeIn}>
              <h3>How long does it take to see results from digital marketing?</h3>
              <p>
                Results vary depending on your goals, industry, and competition. Some clients see
                immediate engagement, while others may take a few weeks to see significant results.
                We provide regular reports and analytics to track progress.
              </p>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="cta-section section">
        <div className="container">
          <motion.div
            className="cta-content"
            initial="hidden"
            animate="visible"
            variants={fadeIn}
          >
            <h2>Ready to Get Started?</h2>
            <p>Contact us today to discuss how we can help you achieve your digital media goals.</p>
            <div className="cta-buttons">
              <a href="/contact" className="button">Contact Us</a>
              <a href="/promote" className="button button-secondary">Promote with Us</a>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default ServicesPage;