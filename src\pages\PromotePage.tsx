import { useRef } from 'react';
import { motion, useInView } from 'framer-motion';
import { Users, BarChart, TrendingUp, Target, MessageSquare, Mail } from 'lucide-react';

// Animation variants
const fadeIn = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0, transition: { duration: 0.6 } }
};

const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2
    }
  }
};

// Promotion packages data
const promotionPackages = [
  {
    id: 1,
    name: 'Basic',
    price: '₦25,000',
    duration: 'per week',
    features: [
      'Featured in 1 WhatsApp TV channel',
      'Basic content creation',
      '3 promotional posts',
      'Performance report',
      'Standard audience targeting'
    ],
    recommended: false
  },
  {
    id: 2,
    name: 'Standard',
    price: '₦75,000',
    duration: 'per month',
    features: [
      'Featured in 3 WhatsApp TV channels',
      'Professional content creation',
      '10 promotional posts',
      'Detailed analytics report',
      'Advanced audience targeting',
      'One collaborative content piece'
    ],
    recommended: true
  },
  {
    id: 3,
    name: 'Premium',
    price: '₦150,000',
    duration: 'per month',
    features: [
      'Featured in all WhatsApp TV channels',
      'Premium content creation',
      'Unlimited promotional posts',
      'Comprehensive analytics dashboard',
      'Precision audience targeting',
      'Three collaborative content pieces',
      'Priority placement',
      'Dedicated account manager'
    ],
    recommended: false
  }
];

// Audience stats
const audienceStats = [
  {
    id: 1,
    stat: '500K+',
    label: 'Total Subscribers',
    icon: <Users size={32} />
  },
  {
    id: 2,
    stat: '85%',
    label: 'Engagement Rate',
    icon: <BarChart size={32} />
  },
  {
    id: 3,
    stat: '12+',
    label: 'Niche Channels',
    icon: <MessageSquare size={32} />
  },
  {
    id: 4,
    stat: '32%',
    label: 'Avg. Conversion Rate',
    icon: <TrendingUp size={32} />
  }
];

const PromotePage = () => {
  // Refs for scroll animations
  const audienceRef = useRef<HTMLDivElement>(null);
  const packagesRef = useRef<HTMLDivElement>(null);
  const processRef = useRef<HTMLDivElement>(null);
  const formRef = useRef<HTMLDivElement>(null);
  
  // Check if sections are in view
  const audienceInView = useInView(audienceRef, { once: true, amount: 0.3 });
  const packagesInView = useInView(packagesRef, { once: true, amount: 0.3 });
  const processInView = useInView(processRef, { once: true, amount: 0.3 });
  const formInView = useInView(formRef, { once: true, amount: 0.3 });

  return (
    <div className="promote-page">
      {/* Hero Section */}
      <section className="page-hero">
        <div className="container">
          <motion.div 
            className="page-hero-content"
            initial="hidden"
            animate="visible"
            variants={staggerContainer}
          >
            <motion.h1 variants={fadeIn}>Advertise With Us</motion.h1>
            <motion.p variants={fadeIn}>
              Promote your brand, products, or services to our engaged Nigerian audience 
              through our WhatsApp TV channels and digital platforms.
            </motion.p>
          </motion.div>
        </div>
      </section>

      {/* Audience Stats Section */}
      <section className="audience-stats-section section" ref={audienceRef}>
        <div className="container">
          <motion.div 
            className="section-header"
            initial="hidden"
            animate={audienceInView ? "visible" : "hidden"}
            variants={fadeIn}
          >
            <h2>Our Audience</h2>
            <div className="section-divider"></div>
            <p>Connect with our engaged Nigerian audience across multiple channels</p>
          </motion.div>
          
          <motion.div 
            className="stats-grid"
            initial="hidden"
            animate={audienceInView ? "visible" : "hidden"}
            variants={staggerContainer}
          >
            {audienceStats.map((stat) => (
              <motion.div className="stat-card" key={stat.id} variants={fadeIn}>
                <div className="stat-icon">
                  {stat.icon}
                </div>
                <h3 className="stat-number">{stat.stat}</h3>
                <p className="stat-label">{stat.label}</p>
              </motion.div>
            ))}
          </motion.div>

          <motion.div 
            className="audience-demographics"
            initial="hidden"
            animate={audienceInView ? "visible" : "hidden"}
            variants={fadeIn}
          >
            <div className="demographics-content">
              <h3>Audience Demographics</h3>
              <div className="demographics-grid">
                <div className="demographic-item">
                  <h4>Age Distribution</h4>
                  <ul>
                    <li><span className="demographic-label">18-24:</span> 35%</li>
                    <li><span className="demographic-label">25-34:</span> 42%</li>
                    <li><span className="demographic-label">35-44:</span> 18%</li>
                    <li><span className="demographic-label">45+:</span> 5%</li>
                  </ul>
                </div>
                <div className="demographic-item">
                  <h4>Gender</h4>
                  <ul>
                    <li><span className="demographic-label">Male:</span> 48%</li>
                    <li><span className="demographic-label">Female:</span> 52%</li>
                  </ul>
                </div>
                <div className="demographic-item">
                  <h4>Location</h4>
                  <ul>
                    <li><span className="demographic-label">Lagos:</span> 45%</li>
                    <li><span className="demographic-label">Abuja:</span> 15%</li>
                    <li><span className="demographic-label">Port Harcourt:</span> 12%</li>
                    <li><span className="demographic-label">Other cities:</span> 28%</li>
                  </ul>
                </div>
                <div className="demographic-item">
                  <h4>Interests</h4>
                  <ul>
                    <li><span className="demographic-label">Entertainment:</span> 65%</li>
                    <li><span className="demographic-label">Business:</span> 48%</li>
                    <li><span className="demographic-label">Technology:</span> 42%</li>
                    <li><span className="demographic-label">Lifestyle:</span> 38%</li>
                  </ul>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Promotion Packages Section */}
      <section className="packages-section section alternate-bg" ref={packagesRef}>
        <div className="container">
          <motion.div 
            className="section-header"
            initial="hidden"
            animate={packagesInView ? "visible" : "hidden"}
            variants={fadeIn}
          >
            <h2>Promotion Packages</h2>
            <div className="section-divider"></div>
            <p>Choose the right package to meet your marketing objectives</p>
          </motion.div>
          
          <motion.div 
            className="packages-grid"
            initial="hidden"
            animate={packagesInView ? "visible" : "hidden"}
            variants={staggerContainer}
          >
            {promotionPackages.map((pkg) => (
              <motion.div 
                className={`package-card ${pkg.recommended ? 'recommended' : ''}`} 
                key={pkg.id} 
                variants={fadeIn}
              >
                {pkg.recommended && <div className="recommended-badge">Most Popular</div>}
                <h3>{pkg.name}</h3>
                <div className="package-price">
                  <span className="price">{pkg.price}</span>
                  <span className="duration">{pkg.duration}</span>
                </div>
                <ul className="package-features">
                  {pkg.features.map((feature, index) => (
                    <li key={index}>{feature}</li>
                  ))}
                </ul>
                <a href="#promotion-form" className="button">Get Started</a>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Promotion Process Section */}
      <section className="process-section section" ref={processRef}>
        <div className="container">
          <motion.div 
            className="section-header"
            initial="hidden"
            animate={processInView ? "visible" : "hidden"}
            variants={fadeIn}
          >
            <h2>How It Works</h2>
            <div className="section-divider"></div>
            <p>Our simple process to get your brand promoted on our platforms</p>
          </motion.div>
          
          <motion.div 
            className="process-steps"
            initial="hidden"
            animate={processInView ? "visible" : "hidden"}
            variants={staggerContainer}
          >
            <motion.div className="process-step" variants={fadeIn}>
              <div className="step-number">1</div>
              <h3>Submit Your Request</h3>
              <p>Fill out our promotion form with your requirements and objectives</p>
            </motion.div>
            
            <motion.div className="process-step" variants={fadeIn}>
              <div className="step-number">2</div>
              <h3>Strategy Development</h3>
              <p>Our team will develop a customized promotion strategy for your brand</p>
            </motion.div>
            
            <motion.div className="process-step" variants={fadeIn}>
              <div className="step-number">3</div>
              <h3>Content Creation</h3>
              <p>We create engaging content tailored to our audience and your brand</p>
            </motion.div>
            
            <motion.div className="process-step" variants={fadeIn}>
              <div className="step-number">4</div>
              <h3>Campaign Launch</h3>
              <p>Your promotion campaign goes live across our selected channels</p>
            </motion.div>
            
            <motion.div className="process-step" variants={fadeIn}>
              <div className="step-number">5</div>
              <h3>Performance Tracking</h3>
              <p>Monitor campaign performance with detailed analytics and reports</p>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Promotion Form Section */}
      <section id="promotion-form" className="form-section section alternate-bg" ref={formRef}>
        <div className="container">
          <motion.div 
            className="section-header"
            initial="hidden"
            animate={formInView ? "visible" : "hidden"}
            variants={fadeIn}
          >
            <h2>Get Started</h2>
            <div className="section-divider"></div>
            <p>Fill out the form below to start promoting your brand with us</p>
          </motion.div>
          
          <motion.div 
            className="form-container"
            initial="hidden"
            animate={formInView ? "visible" : "hidden"}
            variants={fadeIn}
          >
            <form className="promotion-form">
              <div className="form-group">
                <label htmlFor="name">Full Name</label>
                <input type="text" id="name" name="name" required />
              </div>
              
              <div className="form-group">
                <label htmlFor="email">Email Address</label>
                <input type="email" id="email" name="email" required />
              </div>
              
              <div className="form-group">
                <label htmlFor="phone">Phone Number</label>
                <input type="tel" id="phone" name="phone" required />
              </div>
              
              <div className="form-group">
                <label htmlFor="company">Company/Brand Name</label>
                <input type="text" id="company" name="company" required />
              </div>
              
              <div className="form-group">
                <label htmlFor="package">Preferred Package</label>
                <select id="package" name="package" required>
                  <option value="">Select a package</option>
                  <option value="basic">Basic</option>
                  <option value="standard">Standard</option>
                  <option value="premium">Premium</option>
                  <option value="custom">Custom Package</option>
                </select>
              </div>
              
              <div className="form-group">
                <label htmlFor="objectives">Marketing Objectives</label>
                <textarea id="objectives" name="objectives" rows={4} required></textarea>
              </div>
              
              <div className="form-group">
                <label htmlFor="message">Additional Information</label>
                <textarea id="message" name="message" rows={4}></textarea>
              </div>
              
              <button type="submit" className="button">Submit Request</button>
            </form>
            
            <div className="form-contact-info">
              <h3>Contact Us Directly</h3>
              <p>Prefer to discuss your promotion needs directly? Reach out to our advertising team:</p>
              <ul>
                <li>
                  <Mail size={16} />
                  <a href="mailto:<EMAIL>"><EMAIL></a>
                </li>
                <li>
                  <Target size={16} />
                  <span>Response Time: Within 24 hours</span>
                </li>
              </ul>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default PromotePage;