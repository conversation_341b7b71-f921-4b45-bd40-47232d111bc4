/* Admin Components Styles */

.admin-page {
  min-height: 100vh;
  padding: 2rem 0;
  background: var(--color-background-secondary);
}

.admin-header {
  text-align: center;
  margin-bottom: 3rem;
}

.admin-header h1 {
  color: var(--color-text-primary);
  margin-bottom: 0.5rem;
}

.admin-header p {
  color: var(--color-text-secondary);
}

.admin-tabs {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 3rem;
}

.tab-button {
  padding: 0.75rem 1.5rem;
  border: 1px solid var(--color-border);
  background: var(--color-background);
  color: var(--color-text-secondary);
  border-radius: 0.5rem;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
}

.tab-button:hover {
  background: var(--color-background-secondary);
}

.tab-button.active {
  background: var(--color-primary);
  color: white;
  border-color: var(--color-primary);
}

.admin-content {
  max-width: 1200px;
  margin: 0 auto;
}

.tab-content {
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.manage-posts-section {
  background: var(--color-background);
  padding: 2rem;
  border-radius: 1rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.manage-posts-section h2 {
  color: var(--color-text-primary);
  margin-bottom: 1rem;
}

.manage-posts-section p {
  color: var(--color-text-secondary);
  margin-bottom: 2rem;
  line-height: 1.6;
}

.sanity-link {
  color: var(--color-primary);
  text-decoration: none;
  font-weight: 600;
}

.sanity-link:hover {
  text-decoration: underline;
}

.sanity-features {
  text-align: left;
  max-width: 500px;
  margin: 0 auto;
}

.sanity-features h3 {
  color: var(--color-text-primary);
  margin-bottom: 1rem;
}

.sanity-features ul {
  list-style: none;
  padding: 0;
}

.sanity-features li {
  padding: 0.5rem 0;
  color: var(--color-text-secondary);
  border-bottom: 1px solid var(--color-border);
}

.sanity-features li:last-child {
  border-bottom: none;
}

.blog-post-creator {
  max-width: 800px;
  margin: 2rem auto;
  padding: 2rem;
  background: var(--color-background);
  border-radius: 1rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.blog-post-creator h2 {
  color: var(--color-text-primary);
  margin-bottom: 2rem;
  text-align: center;
}

.message {
  padding: 1rem;
  border-radius: 0.5rem;
  margin-bottom: 2rem;
  font-weight: 500;
}

.message.success {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.message.error {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.blog-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group label {
  font-weight: 600;
  color: var(--color-text-primary);
  font-size: 0.875rem;
}

.form-group input,
.form-group select,
.form-group textarea {
  padding: 0.75rem;
  border: 1px solid var(--color-border);
  border-radius: 0.5rem;
  font-size: 1rem;
  background: var(--color-background);
  color: var(--color-text-primary);
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(var(--color-primary-rgb), 0.1);
}

.form-group textarea {
  resize: vertical;
  min-height: 200px;
  font-family: inherit;
  line-height: 1.6;
}

.checkbox-group {
  flex-direction: row;
  align-items: center;
  gap: 0.75rem;
}

.checkbox-group label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
  cursor: pointer;
}

.checkbox-group input[type="checkbox"] {
  width: auto;
  margin: 0;
}

.submit-button {
  background: var(--color-primary);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 0.5rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s ease, transform 0.2s ease;
  margin-top: 1rem;
}

.submit-button:hover:not(:disabled) {
  background: var(--color-primary-dark);
  transform: translateY(-2px);
}

.submit-button:disabled {
  background: var(--color-border);
  cursor: not-allowed;
  transform: none;
}

/* Responsive Design */
@media (max-width: 768px) {
  .blog-post-creator {
    margin: 1rem;
    padding: 1.5rem;
  }
  
  .checkbox-group {
    flex-direction: column;
    align-items: flex-start;
  }
}
