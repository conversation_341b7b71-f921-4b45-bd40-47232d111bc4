import { client, queries, urlFor } from '../lib/sanity';
import { BlogPost, ProcessedBlogPost, BlogPostsResponse, BlogFilters } from '../types/blog';

// Helper function to calculate reading time
const calculateReadingTime = (body: any[]): string => {
  if (!body || body.length === 0) return '1 min read';
  
  const wordsPerMinute = 200;
  const textBlocks = body.filter(block => block._type === 'block');
  const wordCount = textBlocks.reduce((count, block) => {
    if (block.children) {
      const text = block.children.map((child: any) => child.text || '').join(' ');
      return count + text.split(' ').length;
    }
    return count;
  }, 0);
  
  const readingTime = Math.ceil(wordCount / wordsPerMinute);
  return `${readingTime} min read`;
};

// Helper function to extract excerpt from body
const extractExcerpt = (body: any[], maxLength: number = 150): string => {
  if (!body || body.length === 0) {
    console.log('⚠️ No body content found for excerpt');
    return 'No excerpt available.';
  }

  const textBlocks = body.filter(block => block._type === 'block');
  if (textBlocks.length === 0) {
    console.log('⚠️ No text blocks found in body');
    return 'No excerpt available.';
  }

  const firstBlock = textBlocks[0];
  if (firstBlock.children && firstBlock.children.length > 0) {
    const text = firstBlock.children.map((child: any) => child.text || '').join(' ');
    const excerpt = text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
    console.log('📝 Extracted excerpt:', excerpt.substring(0, 50) + '...');
    return excerpt;
  }

  console.log('⚠️ No children found in first block');
  return 'No excerpt available.';
};

// Helper function to process raw blog post data
const processBlogPost = (post: BlogPost): ProcessedBlogPost => {
  try {
    console.log('🔄 Processing post:', post.title);
    console.log('📝 Post data:', {
      id: post._id,
      title: post.title,
      slug: post.slug,
      publishedAt: post.publishedAt,
      author: post.author,
      categories: post.categories,
      bodyLength: post.body?.length || 0
    });

  const imageUrl = post.mainImage ? urlFor(post.mainImage).width(800).height(400).url() : '/images/blog-1.png';
  const excerpt = extractExcerpt(post.body);
  const readingTime = calculateReadingTime(post.body);
  
  // Handle categories - could be array of strings or objects
  const categories = Array.isArray(post.categories) ? post.categories : [];
  const categoryNames = categories.map(cat => typeof cat === 'string' ? cat : cat.title || '');
  const primaryCategory = categoryNames[0] || 'General';
  
  // Format date
  const date = new Date(post.publishedAt).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
  
  const processedPost = {
    id: post._id,
    title: post.title,
    slug: post.slug?.current || '',
    excerpt,
    category: primaryCategory,
    author: typeof post.author === 'string' ? post.author : post.author?.name || 'Unknown',
    date,
    image: imageUrl,
    tags: categoryNames,
    readingTime,
    isWhatsAppContent: post.isWhatsAppContent || false,
    isPremium: post.isPremium || false,
    body: post.body
  };

    console.log('✅ Processed post:', processedPost.title, 'slug:', processedPost.slug);
    return processedPost;
  } catch (error) {
    console.error('❌ Error processing post:', post.title, error);
    // Return a fallback processed post
    return {
      id: post._id,
      title: post.title || 'Untitled',
      slug: post.slug?.current || 'no-slug',
      excerpt: 'Content preview not available.',
      category: 'General',
      author: 'Unknown',
      date: new Date().toLocaleDateString(),
      image: '/images/blog-1.png',
      tags: [],
      readingTime: '1 min read',
      isWhatsAppContent: false,
      isPremium: false,
      body: post.body
    };
  }
};

// Fetch all blog posts
export const getAllBlogPosts = async (filters?: BlogFilters): Promise<BlogPostsResponse> => {
  try {
    console.log('🔍 Fetching blog posts from Sanity...');
    let query = queries.allPosts;
    
    // Add filtering if needed
    if (filters?.category && filters.category !== 'All') {
      query = `*[_type == "post" && defined(publishedAt) && "${filters.category}" in categories[]->title] | order(publishedAt desc) {
        _id,
        title,
        slug,
        publishedAt,
        mainImage,
        "author": author->name,
        "categories": categories[]->title,
        body,
        isWhatsAppContent,
        isPremium
      }`;
    }
    
    // Add pagination
    if (filters?.limit || filters?.offset) {
      const offset = filters.offset || 0;
      const limit = filters.limit || 10;
      query += `[${offset}...${offset + limit}]`;
    }
    
    const posts: BlogPost[] = await client.fetch(query);
    console.log(`✅ Fetched ${posts.length} posts from Sanity`);

    if (posts.length > 0) {
      console.log('📝 First post:', posts[0].title);
    }

    const processedPosts = posts.map(processBlogPost);
    console.log(`🔄 Processed ${processedPosts.length} posts`);
    
    // Filter by search term if provided
    let filteredPosts = processedPosts;
    if (filters?.search) {
      const searchTerm = filters.search.toLowerCase();
      filteredPosts = processedPosts.filter(post =>
        post.title.toLowerCase().includes(searchTerm) ||
        post.excerpt.toLowerCase().includes(searchTerm) ||
        post.tags.some(tag => tag.toLowerCase().includes(searchTerm))
      );
    }
    
    console.log(`📊 Returning ${filteredPosts.length} filtered posts`);

    return {
      posts: filteredPosts,
      total: filteredPosts.length,
      hasMore: false // For now, we'll implement proper pagination later
    };
  } catch (error) {
    console.error('❌ Error fetching blog posts:', error);
    return {
      posts: [],
      total: 0,
      hasMore: false
    };
  }
};

// Fetch a single blog post by slug
export const getBlogPostBySlug = async (slug: string): Promise<ProcessedBlogPost | null> => {
  try {
    const post: BlogPost = await client.fetch(queries.postBySlug, { slug });
    if (!post) return null;
    
    return processBlogPost(post);
  } catch (error) {
    console.error('Error fetching blog post:', error);
    return null;
  }
};

// Fetch featured blog posts (for homepage)
export const getFeaturedBlogPosts = async (limit: number = 3): Promise<ProcessedBlogPost[]> => {
  try {
    const posts: BlogPost[] = await client.fetch(queries.featuredPosts);
    return posts.slice(0, limit).map(processBlogPost);
  } catch (error) {
    console.error('Error fetching featured blog posts:', error);
    return [];
  }
};

// Fetch all categories
export const getAllCategories = async (): Promise<string[]> => {
  try {
    const categories = await client.fetch(queries.allCategories);
    return categories.map((cat: any) => cat.title);
  } catch (error) {
    console.error('Error fetching categories:', error);
    return [];
  }
};
