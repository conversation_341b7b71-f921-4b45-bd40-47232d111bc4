import { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Menu, X, ChevronDown, ArrowUp } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import DarkModeToggle from '../ui/DarkModeToggle';

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null);
  const [isScrolled, setIsScrolled] = useState(false);
  const [showScrollTop, setShowScrollTop] = useState(false);
  const location = useLocation();

  const toggleMenu = () => setIsMenuOpen(!isMenuOpen);

  const toggleDropdown = (dropdown: string) => {
    setActiveDropdown(activeDropdown === dropdown ? null : dropdown);
  };

  // Handle scroll events
  useEffect(() => {
    const handleScroll = () => {
      // Check if page is scrolled more than 50px
      setIsScrolled(window.scrollY > 50);
      // Show scroll-to-top when scrolled more than 300px
      setShowScrollTop(window.scrollY > 300);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const menuItems = [
    { name: 'Home', path: '/' },
    {
      name: 'Services',
      path: '/services',
      dropdown: [
        { name: 'Content Creation', path: '/services#content-creation' },
        { name: 'WhatsApp TV', path: '/services#whatsapp-tv' },
        { name: 'Digital Marketing', path: '/services#digital-marketing' }
      ]
    },
    { name: 'Blog', path: '/blog' },
    { name: 'Join TV', path: '/join-tv' },
    { name: 'Testimonials', path: '/testimonials' },
    { name: 'Promote', path: '/promote' },
    { name: 'Contact', path: '/contact' },
  ];

  return (
    <>
      <header className={`header ${isScrolled ? 'header-scrolled' : ''}`}>
        <div className="container header-container">
        <div className="logo">
          <Link to="/">
            <h1>Trex<span>Media</span></h1>
          </Link>
        </div>

        {/* Desktop Navigation */}
        <nav className="desktop-nav">
          <ul>
            {menuItems.map((item) => (
              <li key={item.name} className={item.dropdown ? 'has-dropdown' : ''}>
                {item.dropdown ? (
                  <>
                    <button
                      onClick={() => toggleDropdown(item.name)}
                      className="dropdown-toggle"
                    >
                      {item.name} <ChevronDown size={16} />
                    </button>
                    <AnimatePresence>
                      {activeDropdown === item.name && (
                        <motion.ul
                          className="dropdown"
                          initial={{ opacity: 0, y: -10 }}
                          animate={{ opacity: 1, y: 0 }}
                          exit={{ opacity: 0, y: -10 }}
                          transition={{ duration: 0.2 }}
                        >
                          {item.dropdown.map((dropdownItem) => (
                            <li key={dropdownItem.name}>
                              <Link
                                to={dropdownItem.path}
                                className={location.hash === dropdownItem.path.split('#')[1] ? 'active' : ''}
                              >
                                {dropdownItem.name}
                              </Link>
                            </li>
                          ))}
                        </motion.ul>
                      )}
                    </AnimatePresence>
                  </>
                ) : (
                  <Link
                    to={item.path}
                    className={location.pathname === item.path ? 'active' : ''}
                  >
                    {item.name}
                  </Link>
                )}
              </li>
            ))}
          </ul>
        </nav>

        {/* Mobile Menu Toggle */}
        <button className="mobile-menu-toggle" onClick={toggleMenu}>
          {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
        </button>

        {/* Mobile Navigation */}
        <AnimatePresence>
          {isMenuOpen && (
            <motion.nav
              className="mobile-nav"
              initial={{ opacity: 0, x: '100%' }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: '100%' }}
              transition={{ duration: 0.3 }}
            >
              <ul>
                {menuItems.map((item) => (
                  <li key={item.name}>
                    {item.dropdown ? (
                      <>
                        <button
                          onClick={() => toggleDropdown(item.name)}
                          className="dropdown-toggle"
                        >
                          {item.name} <ChevronDown size={16} />
                        </button>
                        <AnimatePresence>
                          {activeDropdown === item.name && (
                            <motion.ul
                              className="dropdown"
                              initial={{ opacity: 0, height: 0 }}
                              animate={{ opacity: 1, height: 'auto' }}
                              exit={{ opacity: 0, height: 0 }}
                              transition={{ duration: 0.2 }}
                            >
                              {item.dropdown.map((dropdownItem) => (
                                <li key={dropdownItem.name}>
                                  <Link
                                    to={dropdownItem.path}
                                    onClick={toggleMenu}
                                  >
                                    {dropdownItem.name}
                                  </Link>
                                </li>
                              ))}
                            </motion.ul>
                          )}
                        </AnimatePresence>
                      </>
                    ) : (
                      <Link
                        to={item.path}
                        onClick={toggleMenu}
                        className={location.pathname === item.path ? 'active' : ''}
                      >
                        {item.name}
                      </Link>
                    )}
                  </li>
                ))}
              </ul>
            </motion.nav>
          )}
        </AnimatePresence>

        {/* Dark Mode Toggle */}
        <div className="header-actions">
          <DarkModeToggle />
        </div>
      </div>
    </header>

    {/* Scroll to top button */}
    <AnimatePresence>
      {showScrollTop && (
        <motion.button
          className="scroll-top-button"
          onClick={scrollToTop}
          initial={{ opacity: 0, scale: 0.5 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.5 }}
          whileHover={{ scale: 1.1 }}
          aria-label="Scroll to top"
        >
          <ArrowUp size={20} />
        </motion.button>
      )}
    </AnimatePresence>
    </>
  );
};

export default Header;