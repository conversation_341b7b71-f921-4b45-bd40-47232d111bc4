<?xml version="1.0" encoding="UTF-8"?>
<svg width="1440" height="600" viewBox="0 0 1440 600" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <pattern id="smallDots" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse">
      <circle cx="10" cy="10" r="1" fill="#ffffff" opacity="0.5" />
    </pattern>
    
    <pattern id="mediumDots" x="0" y="0" width="40" height="40" patternUnits="userSpaceOnUse">
      <circle cx="20" cy="20" r="2" fill="#ffffff" opacity="0.3" />
    </pattern>
    
    <pattern id="grid" x="0" y="0" width="80" height="80" patternUnits="userSpaceOnUse">
      <path d="M 80 0 L 0 0 0 80" fill="none" stroke="#ffffff" stroke-width="0.5" opacity="0.1" />
    </pattern>
    
    <linearGradient id="wave-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#ffffff" stop-opacity="0.05" />
      <stop offset="100%" stop-color="#ffffff" stop-opacity="0" />
    </linearGradient>
  </defs>
  
  <!-- Background patterns -->
  <rect width="100%" height="100%" fill="url(#smallDots)" />
  <rect width="100%" height="100%" fill="url(#grid)" />
  <rect width="100%" height="100%" fill="url(#mediumDots)" />
  
  <!-- Decorative elements -->
  <circle cx="100" cy="100" r="50" fill="#ffffff" opacity="0.03" />
  <circle cx="1300" cy="200" r="80" fill="#ffffff" opacity="0.02" />
  <circle cx="700" cy="400" r="100" fill="#ffffff" opacity="0.01" />
  
  <!-- Abstract shapes -->
  <path d="M0,100 C320,200,420,50,740,150 C1060,250,1120,150,1440,200 L1440,600 L0,600 Z" fill="url(#wave-gradient)" />
  <path d="M0,250 C320,350,420,200,740,300 C1060,400,1120,300,1440,350 L1440,600 L0,600 Z" fill="url(#wave-gradient)" opacity="0.5" />
  
  <!-- Floating icons representing blog/content -->
  <rect x="200" y="150" width="30" height="30" rx="5" fill="#ffffff" opacity="0.1" />
  <rect x="1200" y="250" width="40" height="40" rx="5" fill="#ffffff" opacity="0.1" />
  <rect x="600" y="350" width="20" height="20" rx="5" fill="#ffffff" opacity="0.1" />
  
  <path d="M250 165 L230 165 M240 155 L240 175" stroke="#ffffff" stroke-width="2" opacity="0.2" />
  <path d="M1220 270 L1220 270 M1210 260 L1230 280 M1230 260 L1210 280" stroke="#ffffff" stroke-width="2" opacity="0.2" />
  <path d="M610 360 L610 360 M605 355 L615 365 M615 355 L605 365" stroke="#ffffff" stroke-width="2" opacity="0.2" />
</svg>
