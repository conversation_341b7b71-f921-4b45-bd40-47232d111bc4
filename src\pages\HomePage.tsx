import { useRef } from 'react';
import { Link } from 'react-router-dom';
import { motion, useInView } from 'framer-motion';
import { ArrowRight, MessageSquare, Users, TrendingUp, ChevronRight } from 'lucide-react';

// Animation variants
const fadeIn = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0, transition: { duration: 0.6 } }
};

const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2
    }
  }
};

const HomePage = () => {
  // Refs for scroll animations
  const aboutRef = useRef<HTMLDivElement>(null);
  const servicesRef = useRef<HTMLDivElement>(null);
  const whatsappTvRef = useRef<HTMLDivElement>(null);
  const blogRef = useRef<HTMLDivElement>(null);
  const testimonialsRef = useRef<HTMLDivElement>(null);
  const promotionsRef = useRef<HTMLDivElement>(null);
  const contactRef = useRef<HTMLDivElement>(null);

  // Check if sections are in view
  const aboutInView = useInView(aboutRef, { once: true, amount: 0.3 });
  const servicesInView = useInView(servicesRef, { once: true, amount: 0.3 });
  const whatsappTvInView = useInView(whatsappTvRef, { once: true, amount: 0.3 });
  const blogInView = useInView(blogRef, { once: true, amount: 0.3 });
  const testimonialsInView = useInView(testimonialsRef, { once: true, amount: 0.3 });
  const promotionsInView = useInView(promotionsRef, { once: true, amount: 0.3 });
  const contactInView = useInView(contactRef, { once: true, amount: 0.3 });

  return (
    <div className="home-page">
      {/* Hero Section */}
      <section className="hero-section">
        <div className="container hero-container">
          <motion.div
            className="hero-content"
            initial="hidden"
            animate="visible"
            variants={staggerContainer}
          >
            <motion.h1 variants={fadeIn}>Engaging Digital Content for the Modern Nigerian</motion.h1>
            <motion.p variants={fadeIn}>
              Trex Media delivers captivating WhatsApp TV channels and digital entertainment
              tailored for Nigerian audiences. Join our community today!
            </motion.p>
            <motion.div className="hero-buttons" variants={fadeIn}>
              <Link to="/join-tv" className="button">Join TV <ArrowRight size={16} /></Link>
              <Link to="/services" className="button button-secondary">Explore Services</Link>
            </motion.div>
          </motion.div>
          <motion.div
            className="hero-image"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.8 }}
          >
            <img src="/images/hero-image.png" alt="Trex Media Digital Content" />
          </motion.div>
        </div>
      </section>

      {/* About Section */}
      <section className="about-section section" ref={aboutRef}>
        <div className="container">
          <motion.div
            className="section-header"
            initial="hidden"
            animate={aboutInView ? "visible" : "hidden"}
            variants={fadeIn}
          >
            <h2>About Trex Media</h2>
            <div className="section-divider"></div>
          </motion.div>

          <motion.div
            className="about-content"
            initial="hidden"
            animate={aboutInView ? "visible" : "hidden"}
            variants={staggerContainer}
          >
            <motion.div className="about-text" variants={fadeIn}>
              <p>
                Trex Media is a leading WhatsApp TV and entertainment platform based in Nigeria.
                We create and curate engaging digital content across various niches including
                entertainment, business, and romance.
              </p>
              <p>
                Our mission is to deliver high-quality, relevant content to Nigerian audiences
                through accessible platforms. With a focus on innovation and engagement,
                we've built a community of passionate followers who trust our content.
              </p>

              <div className="founder-section">
                <div className="founder-image">
                  <img src="/images/founder.png" alt="Jackson Abetianbe - Founder of Trex Media" />
                </div>
                <div className="founder-content">
                  <h3>Meet the Founder</h3>
                  <p>
                    Trex Media was founded by Jackson Abetianbe during his Senior Secondary School 3 (SS3) years.
                    What began as a passion project in the classroom has evolved into one of Nigeria's most vibrant
                    digital media platforms.
                  </p>
                  <p>
                    "I created Trex Media with the vision of providing quality entertainment and information that
                    resonates with young Nigerians," says Jackson. "Starting this journey while still in SS3 taught me
                    valuable lessons about perseverance, creativity, and connecting with an audience."
                  </p>
                  <p>
                    Today, Jackson continues to lead Trex Media with the same innovative spirit and dedication that
                    sparked its creation, constantly exploring new ways to engage and inspire the community.
                  </p>
                </div>
              </div>

              <Link to="/services" className="text-link">
                Learn more about what we do <ChevronRight size={16} />
              </Link>
            </motion.div>
            <motion.div className="about-image" variants={fadeIn}>
              <img src="/images/about-image.png" alt="About Trex Media" />
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Services Snapshot Section */}
      <section className="services-section section" ref={servicesRef}>
        <div className="container">
          <motion.div
            className="section-header"
            initial="hidden"
            animate={servicesInView ? "visible" : "hidden"}
            variants={fadeIn}
          >
            <h2>Our Services</h2>
            <div className="section-divider"></div>
            <p>Discover how Trex Media can help elevate your digital presence</p>
          </motion.div>

          <motion.div
            className="services-grid"
            initial="hidden"
            animate={servicesInView ? "visible" : "hidden"}
            variants={staggerContainer}
          >
            <motion.div className="service-card" variants={fadeIn}>
              <div className="service-icon">
                <MessageSquare size={32} />
              </div>
              <h3>Content Creation</h3>
              <p>Engaging digital content tailored for Nigerian audiences across various platforms.</p>
            </motion.div>

            <motion.div className="service-card" variants={fadeIn}>
              <div className="service-icon">
                <Users size={32} />
              </div>
              <h3>WhatsApp TV</h3>
              <p>Join our niche-specific WhatsApp channels for daily updates and entertainment.</p>
            </motion.div>

            <motion.div className="service-card" variants={fadeIn}>
              <div className="service-icon">
                <TrendingUp size={32} />
              </div>
              <h3>Digital Marketing</h3>
              <p>Promote your brand to our engaged audience through our various channels.</p>
            </motion.div>
          </motion.div>

          <motion.div
            className="services-cta"
            initial="hidden"
            animate={servicesInView ? "visible" : "hidden"}
            variants={fadeIn}
          >
            <Link to="/services" className="button">View All Services</Link>
          </motion.div>
        </div>
      </section>

      {/* Join WhatsApp TV Section */}
      <section className="whatsapp-tv-section section" ref={whatsappTvRef}>
        <div className="container">
          <motion.div
            className="section-header"
            initial="hidden"
            animate={whatsappTvInView ? "visible" : "hidden"}
            variants={fadeIn}
          >
            <h2>Join Our WhatsApp TVs</h2>
            <div className="section-divider"></div>
            <p>Connect with our channels based on your interests</p>
          </motion.div>

          <motion.div
            className="whatsapp-channels"
            initial="hidden"
            animate={whatsappTvInView ? "visible" : "hidden"}
            variants={staggerContainer}
          >
            <motion.div className="channel-card" variants={fadeIn}>
              <div className="channel-image">
                <img src="/images/entertainment-channel.png" alt="Entertainment Channel" />
              </div>
              <h3>Entertainment</h3>
              <p>Latest movies, music, celebrity news and entertainment updates.</p>
              <Link to="/join-tv#entertainment" className="button">Join Now</Link>
            </motion.div>

            <motion.div className="channel-card" variants={fadeIn}>
              <div className="channel-image">
                <img src="/images/business-channel.png" alt="Business Channel" />
              </div>
              <h3>Business</h3>
              <p>Business tips, market trends, investment opportunities and career advice.</p>
              <Link to="/join-tv#business" className="button">Join Now</Link>
            </motion.div>

            <motion.div className="channel-card" variants={fadeIn}>
              <div className="channel-image">
                <img src="/images/romance-channel.png" alt="Romance Channel" />
              </div>
              <h3>Romance</h3>
              <p>Relationship advice, dating tips, and romantic stories.</p>
              <Link to="/join-tv#romance" className="button">Join Now</Link>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Blog Highlights Section */}
      <section className="blog-section section" ref={blogRef}>
        <div className="container">
          <motion.div
            className="section-header"
            initial="hidden"
            animate={blogInView ? "visible" : "hidden"}
            variants={fadeIn}
          >
            <h2>Latest from Our Blog</h2>
            <div className="section-divider"></div>
            <p>Insights and updates from the Trex Media team</p>
          </motion.div>

          <motion.div
            className="blog-grid"
            initial="hidden"
            animate={blogInView ? "visible" : "hidden"}
            variants={staggerContainer}
          >
            <motion.div className="blog-card" variants={fadeIn}>
              <div className="blog-image">
                <img src="/images/blog-1.png" alt="Blog Post 1" />
              </div>
              <div className="blog-content">
                <span className="blog-date">June 15, 2023</span>
                <h3>The Rise of Digital Entertainment in Nigeria</h3>
                <p>Exploring how digital platforms are transforming entertainment consumption in Nigeria.</p>
                <Link to="/blog/post-1" className="text-link">Read More <ChevronRight size={16} /></Link>
              </div>
            </motion.div>

            <motion.div className="blog-card" variants={fadeIn}>
              <div className="blog-image">
                <img src="/images/blog-2.png" alt="Blog Post 2" />
              </div>
              <div className="blog-content">
                <span className="blog-date">May 28, 2023</span>
                <h3>How WhatsApp TV is Changing Media Consumption</h3>
                <p>A deep dive into the growing popularity of WhatsApp TV channels among Nigerian youth.</p>
                <Link to="/blog/post-2" className="text-link">Read More <ChevronRight size={16} /></Link>
              </div>
            </motion.div>
          </motion.div>

          <motion.div
            className="blog-cta"
            initial="hidden"
            animate={blogInView ? "visible" : "hidden"}
            variants={fadeIn}
          >
            <Link to="/blog" className="button">View All Posts</Link>
          </motion.div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="testimonials-section section" ref={testimonialsRef}>
        <div className="container">
          <motion.div
            className="section-header"
            initial="hidden"
            animate={testimonialsInView ? "visible" : "hidden"}
            variants={fadeIn}
          >
            <h2>What Our Audience Says</h2>
            <div className="section-divider"></div>
          </motion.div>

          <motion.div
            className="testimonials-grid"
            initial="hidden"
            animate={testimonialsInView ? "visible" : "hidden"}
            variants={staggerContainer}
          >
            <motion.div className="testimonial-card" variants={fadeIn}>
              <div className="testimonial-content">
                <p>"Trex Media's WhatsApp TV has become my daily source of entertainment. The content is always fresh and engaging!"</p>
              </div>
              <div className="testimonial-author">
                <img src="/images/testimonial-1.png" alt="Testimonial Author" />
                <div>
                  <h4>Chioma Eze</h4>
                  <p>Entertainment Channel Subscriber</p>
                </div>
              </div>
            </motion.div>

            <motion.div className="testimonial-card" variants={fadeIn}>
              <div className="testimonial-content">
                <p>"The business tips I get from Trex Media have been invaluable for my startup. Highly recommend their business channel!"</p>
              </div>
              <div className="testimonial-author">
                <img src="/images/testimonial-2.png" alt="Testimonial Author" />
                <div>
                  <h4>Oluwaseun Adeyemi</h4>
                  <p>Business Channel Subscriber</p>
                </div>
              </div>
            </motion.div>

            <motion.div className="testimonial-card" variants={fadeIn}>
              <div className="testimonial-content">
                <p>"I promoted my new product through Trex Media and saw immediate engagement. Their audience is truly active and responsive."</p>
              </div>
              <div className="testimonial-author">
                <img src="/images/testimonial-3.png" alt="Testimonial Author" />
                <div>
                  <h4>Tunde Johnson</h4>
                  <p>Business Owner</p>
                </div>
              </div>
            </motion.div>
          </motion.div>

          <motion.div
            className="testimonials-stats"
            initial="hidden"
            animate={testimonialsInView ? "visible" : "hidden"}
            variants={staggerContainer}
          >
            <motion.div className="stat-item" variants={fadeIn}>
              <h3>50K+</h3>
              <p>Active Subscribers</p>
            </motion.div>

            <motion.div className="stat-item" variants={fadeIn}>
              <h3>15+</h3>
              <p>WhatsApp Channels</p>
            </motion.div>

            <motion.div className="stat-item" variants={fadeIn}>
              <h3>200+</h3>
              <p>Successful Promotions</p>
            </motion.div>

            <motion.div className="stat-item" variants={fadeIn}>
              <h3>98%</h3>
              <p>Client Satisfaction</p>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Promotions Section */}
      <section className="promotions-section section" ref={promotionsRef}>
        <div className="container">
          <motion.div
            className="section-header"
            initial="hidden"
            animate={promotionsInView ? "visible" : "hidden"}
            variants={fadeIn}
          >
            <h2>Promote with Trex Media</h2>
            <div className="section-divider"></div>
            <p>Reach our engaged audience with your brand or product</p>
          </motion.div>

          <motion.div
            className="promotions-content"
            initial="hidden"
            animate={promotionsInView ? "visible" : "hidden"}
            variants={staggerContainer}
          >
            <motion.div className="promotions-text" variants={fadeIn}>
              <h3>Why Advertise With Us?</h3>
              <ul className="promotions-list">
                <li>Access to 50,000+ active subscribers across various niches</li>
                <li>Targeted promotion to specific audience demographics</li>
                <li>Affordable packages tailored to your budget</li>
                <li>Detailed engagement reports and analytics</li>
                <li>Professional content creation services available</li>
              </ul>
              <Link to="/promote" className="button">View Promotion Packages</Link>
            </motion.div>
            <motion.div className="promotions-image" variants={fadeIn}>
              <img src="/images/promotions-image.png" alt="Promote with Trex Media" />
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Contact Form Section */}
      <section className="contact-section section" ref={contactRef}>
        <div className="container">
          <motion.div
            className="section-header"
            initial="hidden"
            animate={contactInView ? "visible" : "hidden"}
            variants={fadeIn}
          >
            <h2>Get In Touch</h2>
            <div className="section-divider"></div>
            <p>Have questions or want to collaborate? Reach out to us!</p>
          </motion.div>

          <motion.div
            className="contact-content"
            initial="hidden"
            animate={contactInView ? "visible" : "hidden"}
            variants={staggerContainer}
          >
            <motion.div className="contact-info" variants={fadeIn}>
              <h3>Contact Information</h3>
              <p>Feel free to reach out to us through any of these channels:</p>
              <ul className="contact-list">
                <li>
                  <strong>Email:</strong> <a href="mailto:<EMAIL>"><EMAIL></a>
                </li>
                <li>
                  <strong>WhatsApp:</strong> <a href="https://wa.me/2341234567890">+234 123 456 7890</a>
                </li>
                <li>
                  <strong>Follow Us:</strong>
                  <div className="social-links-small">
                    <a href="https://facebook.com" target="_blank" rel="noopener noreferrer">Facebook</a>
                    <a href="https://twitter.com" target="_blank" rel="noopener noreferrer">Twitter</a>
                    <a href="https://instagram.com" target="_blank" rel="noopener noreferrer">Instagram</a>
                  </div>
                </li>
              </ul>
            </motion.div>

            <motion.form className="contact-form" variants={fadeIn}>
              <div className="form-group">
                <label htmlFor="name">Your Name</label>
                <input type="text" id="name" name="name" placeholder="Enter your name" required />
              </div>

              <div className="form-group">
                <label htmlFor="email">Email Address</label>
                <input type="email" id="email" name="email" placeholder="Enter your email" required />
              </div>

              <div className="form-group">
                <label htmlFor="subject">Subject</label>
                <input type="text" id="subject" name="subject" placeholder="Enter subject" required />
              </div>

              <div className="form-group">
                <label htmlFor="message">Message</label>
                <textarea id="message" name="message" rows={5} placeholder="Enter your message" required></textarea>
              </div>

              <button type="submit" className="button">Send Message</button>
            </motion.form>
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default HomePage;