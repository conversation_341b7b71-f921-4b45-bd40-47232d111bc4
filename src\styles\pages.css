/* Page-specific styles for Testimonials, Promote, and Contact pages */

/* ===== Testimonials Page Styles ===== */

/* Testimonials Grid */
.testimonials-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacing-xl);
  margin-bottom: var(--spacing-2xl);
}

.testimonial-card {
  background-color: var(--color-white);
  border-radius: var(--radius-lg);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  padding: var(--spacing-xl);
  transition: transform var(--transition-normal), box-shadow var(--transition-normal);
}

.testimonial-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.testimonial-header {
  display: flex;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-md);
}

.testimonial-image {
  width: 70px;
  height: 70px;
  border-radius: var(--radius-full);
  overflow: hidden;
  flex-shrink: 0;
}

.testimonial-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.testimonial-meta {
  display: flex;
  flex-direction: column;
}

.testimonial-meta h3 {
  font-size: 1.2rem;
  margin-bottom: var(--spacing-xs);
}

.testimonial-position,
.testimonial-company {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: 0.9rem;
  color: var(--color-gray-600);
  margin-bottom: var(--spacing-xs);
}

.testimonial-rating {
  display: flex;
  gap: var(--spacing-xs);
  margin-top: var(--spacing-xs);
}

.testimonial-content {
  position: relative;
}

.quote-icon {
  position: absolute;
  top: -10px;
  left: -5px;
  color: var(--color-primary);
  opacity: 0.2;
  z-index: 0;
}

.testimonial-content p {
  position: relative;
  z-index: 1;
  font-style: italic;
  color: var(--color-gray-700);
}

/* Case Studies */
.case-studies-container {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2xl);
}

.case-study-card {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: var(--spacing-xl);
  background-color: var(--color-white);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.case-study-image {
  height: 100%;
}

.case-study-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.case-study-content {
  padding: var(--spacing-xl);
}

.case-study-content h3 {
  margin-bottom: var(--spacing-md);
  color: var(--color-primary);
}

.case-study-details {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.case-study-section h4 {
  font-size: 1.1rem;
  margin-bottom: var(--spacing-xs);
  color: var(--color-gray-800);
}

.case-study-section ul {
  list-style-type: none;
  padding-left: 0;
}

.case-study-section li {
  position: relative;
  padding-left: var(--spacing-lg);
  margin-bottom: var(--spacing-xs);
}

.case-study-section li::before {
  content: '✓';
  position: absolute;
  left: 0;
  color: var(--color-primary);
  font-weight: bold;
}

/* Media Kit Section */
.media-kit-container {
  background-color: var(--color-primary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-2xl);
  color: var(--color-white);
  text-align: center;
}

.media-kit-content h2 {
  color: var(--color-white);
}

.media-kit-features {
  list-style-type: none;
  padding: 0;
  margin: var(--spacing-lg) 0;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: var(--spacing-md);
}

.media-kit-features li {
  background-color: rgba(255, 255, 255, 0.1);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-full);
}

.media-kit-container .button {
  background-color: var(--color-white);
  color: var(--color-primary);
}

.media-kit-container .button:hover {
  background-color: var(--color-gray-100);
}

/* ===== Promote Page Styles ===== */

/* Audience Stats */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: var(--spacing-xl);
  margin-bottom: var(--spacing-2xl);
}

.stat-card {
  background-color: var(--color-white);
  border-radius: var(--radius-lg);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  padding: var(--spacing-xl);
  text-align: center;
  transition: transform var(--transition-normal);
}

.stat-card:hover {
  transform: translateY(-5px);
}

.stat-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 70px;
  height: 70px;
  background-color: rgba(98, 0, 234, 0.1);
  color: var(--color-primary);
  border-radius: var(--radius-full);
  margin-bottom: var(--spacing-md);
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--color-primary);
  margin-bottom: var(--spacing-xs);
}

.stat-label {
  font-size: 1.1rem;
  color: var(--color-gray-600);
}

/* Audience Demographics */
.audience-demographics {
  background-color: var(--color-white);
  border-radius: var(--radius-lg);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  padding: var(--spacing-xl);
  margin-top: var(--spacing-2xl);
}

.demographics-content h3 {
  text-align: center;
  margin-bottom: var(--spacing-lg);
}

.demographics-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: var(--spacing-xl);
}

.demographic-item h4 {
  font-size: 1.1rem;
  margin-bottom: var(--spacing-md);
  color: var(--color-primary);
}

.demographic-item ul {
  list-style-type: none;
  padding: 0;
}

.demographic-item li {
  margin-bottom: var(--spacing-sm);
  font-size: 0.95rem;
}

.demographic-label {
  font-weight: 600;
  margin-right: var(--spacing-xs);
}

/* Promotion Packages */
.packages-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacing-xl);
}

.package-card {
  background-color: var(--color-white);
  border-radius: var(--radius-lg);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  padding: var(--spacing-xl);
  position: relative;
  transition: transform var(--transition-normal), box-shadow var(--transition-normal);
}

.package-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.package-card.recommended {
  border: 2px solid var(--color-primary);
  transform: scale(1.05);
  z-index: 1;
}

.package-card.recommended:hover {
  transform: scale(1.05) translateY(-10px);
}

.recommended-badge {
  position: absolute;
  top: -12px;
  left: 50%;
  transform: translateX(-50%);
  background-color: var(--color-primary);
  color: var(--color-white);
  padding: var(--spacing-xs) var(--spacing-md);
  border-radius: var(--radius-full);
  font-size: 0.8rem;
  font-weight: 600;
}

.package-card h3 {
  text-align: center;
  margin-bottom: var(--spacing-md);
}

.package-price {
  text-align: center;
  margin-bottom: var(--spacing-lg);
}

.price {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--color-primary);
}

.duration {
  font-size: 0.9rem;
  color: var(--color-gray-500);
  margin-left: var(--spacing-xs);
}

.package-features {
  list-style-type: none;
  padding: 0;
  margin-bottom: var(--spacing-xl);
}

.package-features li {
  padding: var(--spacing-xs) 0;
  padding-left: var(--spacing-lg);
  position: relative;
}

.package-features li::before {
  content: '✓';
  position: absolute;
  left: 0;
  color: var(--color-primary);
  font-weight: bold;
}

.package-card .button {
  width: 100%;
  text-align: center;
}

/* Process Steps */
.process-steps {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: var(--spacing-xl);
}

.process-step {
  text-align: center;
  position: relative;
}

.process-step:not(:last-child)::after {
  content: '';
  position: absolute;
  top: 30px;
  right: -50%;
  width: 100%;
  height: 2px;
  background-color: var(--color-gray-300);
  z-index: 0;
}

.step-number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  background-color: var(--color-primary);
  color: var(--color-white);
  border-radius: var(--radius-full);
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0 auto var(--spacing-md);
  position: relative;
  z-index: 1;
}

.process-step h3 {
  font-size: 1.2rem;
  margin-bottom: var(--spacing-sm);
}

.process-step p {
  font-size: 0.95rem;
  color: var(--color-gray-600);
}

/* Promotion Form */
.form-container {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: var(--spacing-2xl);
  background-color: var(--color-white);
  border-radius: var(--radius-lg);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  padding: var(--spacing-xl);
}

.promotion-form {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-md);
}

.form-group {
  margin-bottom: var(--spacing-md);
}

.form-group:nth-last-child(3),
.form-group:nth-last-child(2),
.form-group:nth-last-child(1) {
  grid-column: span 2;
}

.form-contact-info {
  background-color: var(--color-gray-100);
  padding: var(--spacing-xl);
  border-radius: var(--radius-lg);
  align-self: start;
}

.form-contact-info h3 {
  margin-bottom: var(--spacing-md);
}

.form-contact-info ul {
  list-style-type: none;
  padding: 0;
  margin-top: var(--spacing-lg);
}

.form-contact-info li {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-md);
}

/* ===== Contact Page Styles ===== */

/* Contact Info Cards */
.contact-info-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: var(--spacing-xl);
  margin-bottom: var(--spacing-2xl);
}

.contact-info-card {
  background-color: var(--color-white);
  border-radius: var(--radius-lg);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  padding: var(--spacing-xl);
  text-align: center;
  transition: transform var(--transition-normal);
}

.contact-info-card:hover {
  transform: translateY(-5px);
}

.contact-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 70px;
  height: 70px;
  background-color: rgba(98, 0, 234, 0.1);
  color: var(--color-primary);
  border-radius: var(--radius-full);
  margin-bottom: var(--spacing-md);
}

.contact-info-card h3 {
  margin-bottom: var(--spacing-md);
}

.contact-info-card p {
  margin-bottom: var(--spacing-xs);
  color: var(--color-gray-600);
}

.contact-info-card a {
  display: block;
  margin-bottom: var(--spacing-md);
  font-weight: 600;
}

/* Social Media Links */
.social-media-container {
  text-align: center;
  margin-top: var(--spacing-2xl);
}

.social-media-container h3 {
  margin-bottom: var(--spacing-md);
}

.social-media-links {
  display: flex;
  justify-content: center;
  gap: var(--spacing-md);
}

.social-media-links a {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  border-radius: var(--radius-full);
  background-color: var(--color-gray-100);
  color: var(--color-primary);
  transition: all var(--transition-fast);
}

.social-media-links a:hover {
  background-color: var(--color-primary);
  color: var(--color-white);
  transform: translateY(-5px);
}

/* Contact Form */
.contact-form-container {
  background-color: var(--color-white);
  border-radius: var(--radius-lg);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  padding: var(--spacing-xl);
}

.contact-form {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-md);
}

.form-group label {
  display: block;
  margin-bottom: var(--spacing-xs);
  font-weight: 600;
}

.form-group input,
.form-group textarea,
.form-group select {
  width: 100%;
  padding: var(--spacing-md);
  border: 1px solid var(--color-gray-300);
  border-radius: var(--radius-md);
  font-family: var(--font-body);
  font-size: 1rem;
  transition: border-color var(--transition-fast);
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
  outline: none;
  border-color: var(--color-primary);
}

.checkbox-group {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.checkbox-group input {
  width: auto;
}

.checkbox-group label {
  margin-bottom: 0;
  font-weight: normal;
}

/* Map Section */
.map-section {
  height: 400px;
  margin-top: var(--spacing-2xl);
}

.map-container {
  height: 100%;
  width: 100%;
}

.map-placeholder {
  height: 100%;
  width: 100%;
  background-color: var(--color-gray-200);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-gray-600);
  font-size: 1.2rem;
}

/* FAQ Section */
.faq-section {
  background: linear-gradient(135deg, var(--color-primary-dark) 0%, var(--color-primary) 100%);
  position: relative;
  overflow: hidden;
  color: var(--color-white);
  padding-top: var(--spacing-3xl);
  padding-bottom: var(--spacing-3xl);
}

.faq-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at 10% 90%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
  z-index: 0;
}

.faq-section .container {
  position: relative;
  z-index: 1;
}

.faq-section .section-header h2 {
  color: var(--color-white);
  position: relative;
  display: inline-block;
}

.faq-section .section-header h2::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background-color: var(--color-secondary);
  border-radius: var(--radius-full);
}

.faq-section .section-divider {
  background-color: var(--color-white);
  opacity: 0.2;
}

.faq-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-xl);
  margin-top: var(--spacing-xl);
}

.faq-item {
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  padding: var(--spacing-xl);
  transition: all var(--transition-normal);
  border: 2px solid var(--color-white);
  position: relative;
  overflow: hidden;
  margin-bottom: var(--spacing-md);
  backdrop-filter: blur(10px);
  color: var(--color-gray-900);
}

.faq-item::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 0;
  background: linear-gradient(to bottom, var(--color-primary) 0%, var(--color-secondary) 100%);
  transition: height var(--transition-normal);
}

.faq-item:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-xl);
  border-color: var(--color-secondary);
  background-color: rgba(255, 255, 255, 1);
}

.faq-item:hover::after {
  height: 100%;
}

.faq-item h3 {
  font-size: 1.2rem;
  margin-bottom: var(--spacing-md);
  color: var(--color-primary-dark);
  transition: all var(--transition-normal);
  position: relative;
  padding-left: var(--spacing-lg);
  font-weight: 700;
}

.faq-item h3::before {
  content: '→';
  position: absolute;
  left: 0;
  color: var(--color-secondary);
  transition: transform var(--transition-normal);
}

.faq-item:hover h3 {
  color: var(--color-primary);
  transform: translateX(var(--spacing-xs));
}

.faq-item:hover h3::before {
  transform: translateX(var(--spacing-xs));
}

.faq-item p {
  color: var(--color-gray-800);
  background-color: rgba(124, 58, 237, 0.15);
  padding: var(--spacing-md);
  border-radius: var(--radius-md);
  border-left: 3px solid var(--color-primary);
  margin-left: var(--spacing-md);
  box-shadow: var(--shadow-md);
  font-weight: 500;
}

/* Responsive Styles */
@media (max-width: 1024px) {
  .testimonials-grid,
  .stats-grid,
  .contact-info-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .demographics-grid,
  .process-steps {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-xl) var(--spacing-md);
  }

  .process-step:not(:last-child)::after {
    display: none;
  }

  .case-study-card {
    grid-template-columns: 1fr;
  }

  .case-study-image {
    height: 250px;
  }

  .form-container {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .packages-grid {
    grid-template-columns: 1fr;
  }

  .package-card.recommended {
    transform: scale(1);
  }

  .package-card.recommended:hover {
    transform: translateY(-10px);
  }

  .promotion-form {
    grid-template-columns: 1fr;
  }

  .form-group:nth-last-child(3),
  .form-group:nth-last-child(2),
  .form-group:nth-last-child(1) {
    grid-column: span 1;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .faq-container {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .testimonials-grid,
  .stats-grid,
  .contact-info-grid,
  .demographics-grid {
    grid-template-columns: 1fr;
  }

  .social-media-links {
    flex-wrap: wrap;
    justify-content: center;
  }
}

/* Dark Mode Adjustments */
[data-theme="dark"] .faq-section {
  background: linear-gradient(135deg, var(--color-gray-900) 0%, var(--color-primary-dark) 100%);
}

[data-theme="dark"] .faq-item {
  background-color: rgba(39, 39, 42, 0.9);
  border-color: var(--color-gray-700);
  color: var(--color-white);
}

[data-theme="dark"] .faq-item p {
  color: var(--color-gray-300);
  background-color: rgba(124, 58, 237, 0.25);
  font-weight: 500;
}

[data-theme="dark"] .faq-item:hover {
  background-color: rgba(124, 58, 237, 0.35);
  border-color: var(--color-secondary);
  box-shadow: var(--shadow-xl);
}