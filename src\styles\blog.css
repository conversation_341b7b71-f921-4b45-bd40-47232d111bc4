/* Blog Page Styles */

/* Loading and Error States */
.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 0;
  text-align: center;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--color-border);
  border-top: 4px solid var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-message {
  text-align: center;
  padding: 4rem 0;
}

.error-message h3 {
  color: var(--color-error, #dc3545);
  margin-bottom: 1rem;
}

.error-message p {
  margin-bottom: 2rem;
  color: var(--color-text-secondary);
}

/* Blog Hero Section */
.blog-page {
  background-color: var(--color-gray-50);
}

.blog-page .page-hero {
  background: linear-gradient(135deg, var(--color-primary-dark) 0%, var(--color-primary) 100%);
  color: var(--color-white);
  padding: var(--spacing-3xl) 0;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.blog-page .page-hero::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  z-index: 1;
}

.blog-page .page-hero::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-image: url('/images/blog-pattern.svg');
  background-size: cover;
  opacity: 0.1;
  z-index: 1;
}

.blog-page .page-hero-content {
  position: relative;
  z-index: 2;
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
}

.blog-page .page-hero-content h1 {
  color: var(--color-white);
  font-size: 3.5rem;
  margin-bottom: var(--spacing-md);
  position: relative;
  display: inline-block;
}

.blog-page .page-hero-content h1::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background-color: var(--color-secondary);
  border-radius: var(--radius-full);
}

.blog-page .page-hero-content p {
  font-size: 1.2rem;
  opacity: 0.9;
  max-width: 600px;
  margin: 0 auto;
}

/* Featured Post Section */
.featured-post {
  position: relative;
}

.featured-post::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, rgba(124, 58, 237, 0.05) 0%, transparent 70%);
  z-index: 0;
}

.featured-post .container {
  position: relative;
  z-index: 1;
}

.featured-post-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-2xl);
  align-items: center;
  background-color: var(--color-white);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-lg);
  margin-top: var(--spacing-xl);
  border: 1px solid var(--color-gray-200);
  transition: all var(--transition-normal);
  position: relative;
}

.featured-post-container:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-xl);
  border-color: var(--color-primary-light);
}

.featured-post-image {
  position: relative;
  height: 100%;
  min-height: 400px;
  overflow: hidden;
}

.featured-post-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform var(--transition-normal);
}

.featured-post-container:hover .featured-post-image img {
  transform: scale(1.05);
}

.post-category {
  position: absolute;
  top: var(--spacing-md);
  left: var(--spacing-md);
  background-color: var(--color-primary);
  color: var(--color-white);
  padding: 0.25rem 0.75rem;
  border-radius: var(--radius-full);
  font-size: 0.8rem;
  font-weight: 600;
  z-index: 2;
  box-shadow: var(--shadow-sm);
}

.featured-post-content {
  padding: var(--spacing-xl);
  background-color: var(--color-white);
  position: relative;
}

.featured-post-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 5px;
  height: 100%;
  background: linear-gradient(to bottom, var(--color-primary) 0%, var(--color-secondary) 100%);
}

.featured-post-content h3 {
  font-size: 1.8rem;
  margin-bottom: var(--spacing-md);
  line-height: 1.3;
}

.post-meta {
  display: flex;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-md);
  color: var(--color-gray-600);
  font-size: 0.9rem;
}

.post-meta span {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.post-tags {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-lg);
}

.tag {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
  background-color: var(--color-gray-100);
  color: var(--color-gray-700);
  padding: 0.25rem 0.75rem;
  border-radius: var(--radius-full);
  font-size: 0.8rem;
  transition: all var(--transition-normal);
}

.tag:hover {
  background-color: var(--color-primary-light);
  color: var(--color-white);
  transform: translateY(-2px);
}

.reading-time {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  color: var(--color-secondary);
  font-weight: 500;
  margin-top: var(--spacing-md);
}

/* Blog Filters */
.blog-filters {
  margin-bottom: var(--spacing-xl);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.filter-controls {
  display: flex;
  justify-content: center;
  gap: var(--spacing-md);
  margin-top: var(--spacing-md);
}

.filter-toggle {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.clear-filters {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  background-color: var(--color-gray-200);
  color: var(--color-gray-700);
}

.clear-filters:hover {
  background-color: var(--color-gray-300);
  color: var(--color-gray-900);
}

.clear-search {
  position: absolute;
  right: var(--spacing-md);
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--color-gray-500);
  cursor: pointer;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: none;
}

.clear-search:hover {
  color: var(--color-primary);
  background: none;
  transform: translateY(-50%) scale(1.1);
  box-shadow: none;
}

.results-count {
  text-align: center;
  color: var(--color-gray-600);
  margin-bottom: var(--spacing-lg);
  font-size: 0.9rem;
  background-color: var(--color-white);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-full);
  display: inline-block;
  margin: 0 auto var(--spacing-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--color-gray-200);
}

.search-container {
  position: relative;
  max-width: 500px;
  margin: 0 auto;
  width: 100%;
}

.search-container svg {
  position: absolute;
  left: var(--spacing-md);
  top: 50%;
  transform: translateY(-50%);
  color: var(--color-gray-500);
}

.search-container input {
  width: 100%;
  padding: var(--spacing-md) var(--spacing-md) var(--spacing-md) calc(var(--spacing-md) * 2 + 18px);
  border: 2px solid var(--color-gray-200);
  border-radius: var(--radius-full);
  font-family: var(--font-body);
  font-size: 1rem;
  transition: all var(--transition-normal);
  background-color: var(--color-white);
  color: var(--color-gray-800);
}

.search-container input:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(124, 58, 237, 0.1);
}

.category-filters {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-md);
}

.category-filter {
  background-color: var(--color-white);
  color: var(--color-gray-700);
  border: 1px solid var(--color-gray-200);
  padding: 0.5rem 1rem;
  border-radius: var(--radius-full);
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-normal);
  box-shadow: var(--shadow-sm);
}

.category-filter:hover {
  background-color: var(--color-gray-100);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  border-color: var(--color-primary-light);
}

.category-filter.active {
  background-color: var(--color-primary);
  color: var(--color-white);
  border-color: var(--color-primary);
  box-shadow: var(--shadow-md);
}

/* Blog Grid */
.blog-list {
  position: relative;
  padding-top: var(--spacing-2xl);
  padding-bottom: var(--spacing-3xl);
  background-color: var(--color-gray-50);
}

.blog-list::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url('/images/dots-pattern.svg');
  opacity: 0.4;
  z-index: 0;
}

.blog-list .container {
  position: relative;
  z-index: 1;
}

.blog-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacing-xl);
  margin-bottom: var(--spacing-2xl);
}

.blog-card {
  background-color: var(--color-white);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
  border: 1px solid var(--color-gray-200);
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
}

.blog-card::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 5px;
  background: linear-gradient(to right, var(--color-primary) 0%, var(--color-secondary) 100%);
  transform: scaleX(0);
  transform-origin: left;
  transition: transform var(--transition-normal);
}

.blog-card:hover {
  transform: translateY(-10px);
  box-shadow: var(--shadow-xl);
  border-color: var(--color-primary-light);
}

.blog-card:hover::after {
  transform: scaleX(1);
}

.blog-card-image {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.blog-card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform var(--transition-normal);
}

.blog-card:hover .blog-card-image img {
  transform: scale(1.05);
}

.blog-card-content {
  padding: var(--spacing-lg);
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: var(--color-white);
  border-top: 1px solid var(--color-gray-100);
}

.blog-card-content h3 {
  font-size: 1.3rem;
  margin-bottom: var(--spacing-sm);
  line-height: 1.4;
}

.blog-card-content p {
  color: var(--color-gray-600);
  margin-bottom: var(--spacing-md);
  flex: 1;
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
  border-top: 1px solid var(--color-gray-200);
  padding-top: var(--spacing-md);
  background-color: var(--color-white);
}

.reading-time {
  font-size: 0.85rem;
  color: var(--color-secondary-dark);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.text-link {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-weight: 600;
  color: var(--color-primary);
  transition: all var(--transition-normal);
  margin-top: auto;
}

.text-link:hover {
  color: var(--color-primary-dark);
  gap: var(--spacing-sm);
}

/* No Results */
.no-results {
  grid-column: 1 / -1;
  text-align: center;
  padding: var(--spacing-2xl);
  background-color: var(--color-gray-50);
  border-radius: var(--radius-lg);
  border: 1px dashed var(--color-gray-300);
}

.no-results h3 {
  color: var(--color-gray-700);
  margin-bottom: var(--spacing-sm);
}

.no-results p {
  color: var(--color-gray-500);
}

/* Pagination */
.pagination {
  display: flex;
  justify-content: center;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-xl);
}

.pagination-button {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-md);
  background-color: var(--color-white);
  border: 1px solid var(--color-gray-200);
  color: var(--color-gray-700);
  font-weight: 600;
  transition: all var(--transition-normal);
}

.pagination-button:hover {
  background-color: var(--color-gray-100);
  transform: translateY(-2px);
}

.pagination-button.active {
  background-color: var(--color-primary);
  color: var(--color-white);
  border-color: var(--color-primary);
}

.pagination-button:last-child {
  width: auto;
  padding: 0 var(--spacing-md);
}

/* Newsletter Section */
.newsletter-section {
  background: linear-gradient(135deg, var(--color-gray-900) 0%, var(--color-primary-dark) 100%);
  color: var(--color-white);
  position: relative;
  overflow: hidden;
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
}

.newsletter-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at 10% 20%, rgba(124, 58, 237, 0.2) 0%, transparent 50%);
  z-index: 1;
}

.newsletter-container {
  position: relative;
  z-index: 2;
  max-width: 600px;
  margin: 0 auto;
  text-align: center;
}

.newsletter-content h2 {
  color: var(--color-white);
  margin-bottom: var(--spacing-md);
}

.newsletter-content p {
  color: var(--color-gray-300);
  margin-bottom: var(--spacing-xl);
}

.newsletter-form {
  display: flex;
  gap: var(--spacing-sm);
}

.newsletter-form input {
  flex: 1;
  padding: var(--spacing-md) var(--spacing-lg);
  border: none;
  border-radius: var(--radius-md);
  font-family: var(--font-body);
  font-size: 1rem;
}

.newsletter-form input:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(124, 58, 237, 0.3);
}

.newsletter-form .button {
  padding: var(--spacing-md) var(--spacing-xl);
}

/* Responsive Styles */
@media (max-width: 1024px) {
  .blog-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .featured-post-container {
    grid-template-columns: 1fr;
  }

  .featured-post-image {
    min-height: 300px;
  }
}

@media (max-width: 768px) {
  .blog-page .page-hero-content h1 {
    font-size: 2.5rem;
  }

  .newsletter-form {
    flex-direction: column;
  }

  .post-meta {
    flex-direction: column;
    gap: var(--spacing-xs);
  }
}

@media (max-width: 640px) {
  .blog-grid {
    grid-template-columns: 1fr;
  }

  .category-filters {
    flex-direction: column;
    align-items: center;
  }

  .category-filter {
    width: 100%;
    max-width: 300px;
  }

  .blog-page .page-hero-content h1 {
    font-size: 2rem;
  }
}

/* Dark Mode Adjustments */
[data-theme="dark"] .blog-card,
[data-theme="dark"] .featured-post-container {
  background-color: var(--color-gray-800);
  border-color: var(--color-gray-700);
}

[data-theme="dark"] .tag {
  background-color: var(--color-gray-700);
  color: var(--color-gray-300);
}

[data-theme="dark"] .category-filter {
  background-color: var(--color-gray-700);
  color: var(--color-gray-300);
}

[data-theme="dark"] .category-filter:hover {
  background-color: var(--color-gray-600);
}

[data-theme="dark"] .no-results {
  background-color: var(--color-gray-800);
  border-color: var(--color-gray-700);
}

[data-theme="dark"] .pagination-button {
  background-color: var(--color-gray-800);
  border-color: var(--color-gray-700);
  color: var(--color-gray-300);
}

[data-theme="dark"] .pagination-button:hover {
  background-color: var(--color-gray-700);
}
