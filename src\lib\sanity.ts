import { createClient } from '@sanity/client';
import imageUrlBuilder from '@sanity/image-url';

// Create the Sanity client
export const client = createClient({
  projectId: '535k6265', // Your Sanity project ID
  dataset: 'production', // Your dataset name
  useCdn: true, // Enable CDN for faster response times
  apiVersion: '2024-01-01', // Use current date for API version
});

// Create image URL builder
const builder = imageUrlBuilder(client);

// Helper function to generate image URLs
export const urlFor = (source: any) => builder.image(source);

// GROQ queries for fetching data
export const queries = {
  // Get all published posts
  allPosts: `*[_type == "post"] | order(_createdAt desc) {
    _id,
    title,
    slug,
    publishedAt,
    mainImage,
    "author": author->name,
    "categories": categories[]->title,
    body,
    isWhatsAppContent,
    isPremium
  }`,

  // Get a single post by slug
  postBySlug: `*[_type == "post" && slug.current == $slug][0] {
    _id,
    title,
    slug,
    publishedAt,
    mainImage,
    "author": author->{name, image},
    "categories": categories[]->title,
    body,
    isWhatsAppContent,
    isPremium
  }`,

  // Get featured posts (latest 3)
  featuredPosts: `*[_type == "post" && defined(publishedAt)] | order(publishedAt desc)[0...3] {
    _id,
    title,
    slug,
    publishedAt,
    mainImage,
    "author": author->name,
    "categories": categories[]->title,
    body[0...2],
    isWhatsAppContent,
    isPremium
  }`,

  // Get all categories
  allCategories: `*[_type == "category"] | order(title asc) {
    _id,
    title,
    description
  }`,

  // Get all authors
  allAuthors: <AUTHORS>
    _id,
    name,
    image,
    bio
  }`
};
