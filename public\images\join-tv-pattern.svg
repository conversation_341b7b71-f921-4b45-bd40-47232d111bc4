<?xml version="1.0" encoding="UTF-8"?>
<svg width="1440" height="600" viewBox="0 0 1440 600" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <pattern id="smallDots" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse">
      <circle cx="10" cy="10" r="1" fill="#ffffff" opacity="0.5" />
    </pattern>
    
    <pattern id="grid" x="0" y="0" width="80" height="80" patternUnits="userSpaceOnUse">
      <path d="M 80 0 L 0 0 0 80" fill="none" stroke="#ffffff" stroke-width="0.5" opacity="0.1" />
    </pattern>
    
    <linearGradient id="wave-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#ffffff" stop-opacity="0.05" />
      <stop offset="100%" stop-color="#ffffff" stop-opacity="0" />
    </linearGradient>
  </defs>
  
  <!-- Background patterns -->
  <rect width="100%" height="100%" fill="url(#smallDots)" />
  <rect width="100%" height="100%" fill="url(#grid)" />
  
  <!-- Decorative elements -->
  <circle cx="100" cy="100" r="50" fill="#ffffff" opacity="0.03" />
  <circle cx="1300" cy="200" r="80" fill="#ffffff" opacity="0.02" />
  <circle cx="700" cy="400" r="100" fill="#ffffff" opacity="0.01" />
  
  <!-- Abstract shapes -->
  <path d="M0,100 C320,200,420,50,740,150 C1060,250,1120,150,1440,200 L1440,600 L0,600 Z" fill="url(#wave-gradient)" />
  <path d="M0,250 C320,350,420,200,740,300 C1060,400,1120,300,1440,350 L1440,600 L0,600 Z" fill="url(#wave-gradient)" opacity="0.5" />
  
  <!-- WhatsApp TV related icons -->
  <g opacity="0.2" fill="#ffffff">
    <!-- Phone icon -->
    <rect x="200" y="150" width="20" height="40" rx="3" />
    <rect x="195" y="155" width="30" height="30" rx="3" />
    <circle cx="210" cy="195" r="3" />
    
    <!-- TV icon -->
    <rect x="1200" y="250" width="40" height="30" rx="3" />
    <rect x="1210" y="280" width="20" height="5" />
    
    <!-- Message icon -->
    <rect x="600" y="350" width="30" height="20" rx="5" />
    <path d="M600 360 L595 370 L605 365" />
    
    <!-- Chat bubbles -->
    <rect x="400" y="200" width="25" height="15" rx="5" />
    <rect x="410" y="220" width="25" height="15" rx="5" transform="rotate(-10)" />
    
    <!-- Play button -->
    <circle cx="900" cy="300" r="15" />
    <path d="M895 293 L910 300 L895 307 Z" />
  </g>
</svg>
